{"version": 3, "file": "css/app.css", "mappings": "AAAA;;;;;EAAA,CAAC,4BCSG,qQAIA,sMAIA,iKAIA,wNAIA,iRAIA,iPAIA,iRAGF,2BACA,qBAMA,wMACA,mGACA,4EAOA,gDC2OI,4BALI,CDpOR,0BACA,0BAKA,wBACA,6BACA,qBACA,6BAEA,yBACA,8BAEA,wCACA,kCACA,0BACA,kCAEA,sCACA,iCACA,yBACA,iCAGA,wBAEA,wBACA,+BACA,0BAEA,8BACA,qCAGE,qCAGF,wBACA,0BAGA,sBACA,wBACA,0BACA,+CAEA,0BACA,6BACA,6BACA,2BACA,4BACA,mDACA,8BAGA,gDACA,oDACA,mDACA,uDAIA,8BACA,6BACA,2CAIA,8BACA,qCACA,gCACA,uCE/GF,iBAGE,sBAeE,6CANJ,MAOM,wBAcN,KASE,8BACA,0CAFA,mCAFA,2BAJA,uCD6OI,kCALI,CCtOR,uCACA,uCAJA,SAMA,oCAGA,CASF,GAGE,SACA,wCAFA,aCmnB4B,CDpnB5B,cAIA,WCynB4B,CD/mB9B,0CAOE,8BAFA,eEqDqB,CFpDrB,eCwjB4B,CD5jB5B,mBCwjB4B,CDzjB5B,YAMA,CAGF,ODmMM,iBALI,CCzLV,OD8LM,oBALI,CCpLV,ODyLM,mBALI,CC/KV,ODoLM,oBALI,CCrKV,cD0KM,iBALI,CC1JV,EAEE,mBADA,YCyV0B,CD9U5B,YAEE,YADA,0EAEA,oEAMF,QAEE,kBACA,oBAFA,kBAEA,CAMF,MAEE,kBAGF,SAIE,mBADA,YACA,CAGF,wBAIE,gBAGF,GACE,eE9KiB,CFmLnB,GACE,oBACA,cAMF,WACE,gBAQF,SAEE,kBCsa4B,CD9Z9B,aD6EM,aALI,CCjEV,WAEE,wCADA,eACA,CASF,QD2DM,eALI,CClDR,cAFA,kBAGA,wBAGF,kBACA,cAKA,EACE,8DACA,oBE3HgB,CF6HhB,QACE,mDACA,yBE9HoB,CFwItB,4DAEE,cACA,qBAOJ,kBAIE,oCCiV4B,CFlUxB,aALI,CCFV,IACE,cDMI,cCJJ,mBADA,aAEA,aDFQ,CCOR,SAEE,cDJE,iBALI,CCUN,kBAIJ,KAGE,qBADA,2BDXI,aCYJ,CAGA,OACE,cAIJ,IAIE,qCCo5CkC,CExrDhC,oBHmSF,uBCo5CkC,CF36C9B,aALI,CC0BR,wBGjSE,CHuSF,QD3BI,cC4BF,SDjCM,CC4CV,OACE,gBAMF,QAEE,sBAQF,MAEE,yBADA,mBACA,CAGF,QAGE,+BCwZ4B,CDzZ5B,qBE9HqB,CF6HrB,kBE7HqB,CFgIrB,gBAOF,GAEE,mBACA,gCAGF,2BAQE,cAAa,CAFb,oBAEA,CAQF,MACE,qBAMF,OAEE,gBAQF,iCACE,UAKF,sCAME,oBD3HI,iBALI,CCkIR,oBAHA,QAGA,CAIF,cAEE,oBAKF,cACE,eAGF,OAGE,iBAGA,gBACE,UAOJ,0IACE,uBAQF,gDAIE,0BAGE,4GACE,eAON,mBAEE,kBADA,SACA,CAKF,SACE,gBAUF,SAIE,QAAO,CADP,SAFA,YACA,SAEA,CAQF,OACE,WDjNI,gBALI,CC4NR,oBAHA,mBCgN4B,CDjN5B,UADA,UAKA,CAEA,SACE,WAOJ,+OAOE,UAGF,4BACE,YASF,cAEE,6BADA,mBACA,CAmBF,4BACE,wBAKF,+BACE,UAOF,6BAEE,0BADA,YACA,CAFF,uBAEE,0BADA,YACA,CAKF,OACE,qBAKF,OACE,SAOF,QAEE,eADA,iBACA,CAQF,SACE,wBAQF,SACE,uBIpkBF,MLmQM,oBALI,CK5PR,eHwoB4B,CGnoB5B,WL4PI,cEiXwB,CG7mB5B,sBAIE,eHynBkB,CGxnBlB,eHwmB0B,CG7mB5B,WL4PI,gBEiXwB,CG7mB5B,WL4PI,gBEiXwB,CG7mB5B,sBAIE,eHynBkB,CGxnBlB,eHwmB0B,CG7mB5B,WL4PI,gBEiXwB,CG7mB5B,WL4PI,cEiXwB,CG7mB5B,sBAIE,eHynBkB,CGxnBlB,eHwmB0B,CG7mB5B,WL4PI,gBEiXwB,CGhlB9B,4BC3DE,gBADA,cACA,CD8DF,kBACE,qBAEA,mCACE,kBHkoB0B,CGxnB9B,YL8MM,aALI,CKvMR,yBAIF,YLwMM,qBKvMJ,kBLkMQ,CK/LR,wBACE,gBAIJ,mBAIE,cL2LI,aALI,CKxLR,kBFvFO,CEsFP,gBFtBS,CE2BT,0BACE,aE1FJ,0BCCE,YAHA,cAGA,CDDF,eAEE,kCLyjDkC,CKxjDlC,2DHGE,sCGLF,cCAA,CDcF,QAEE,qBAGF,YAEE,aAAY,CADZ,mBACA,CAGF,gBAEE,gCPuPI,aEqzC8B,CO9kDlC,oFCHA,sBACA,gBAKA,iBADA,kBADA,yCADA,0CADA,UAIA,CCsDE,wBF5CE,yBACE,eNkBe,EQyBnB,wBF5CE,uCACE,eNkBe,EQyBnB,wBF5CE,qDACE,eNkBe,EQyBnB,yBF5CE,mEACE,gBNkBe,ESlCvB,MAEI,qJAKF,KCNA,mBACA,gBACA,aACA,eAIA,yCADA,0CADA,sCAEA,CDEE,OCOF,cAKA,8BAHA,eAEA,yCADA,0CAFA,UAIA,CA+CI,KACE,YAGF,iBApCJ,cACA,WAcA,cACE,cACA,WAFF,cACE,cACA,UAFF,cACE,cACA,qBAFF,cACE,cACA,UAFF,cACE,cACA,UAFF,cACE,cACA,qBA+BE,UAhDJ,cACA,WAqDQ,OAhEN,cACA,kBA+DM,OAhEN,cACA,mBA+DM,OAhEN,cACA,UA+DM,OAhEN,cACA,mBA+DM,OAhEN,cACA,mBA+DM,OAhEN,cACA,UA+DM,OAhEN,cACA,mBA+DM,OAhEN,cACA,mBA+DM,OAhEN,cACA,UA+DM,QAhEN,cACA,mBA+DM,QAhEN,cACA,mBA+DM,QAhEN,cACA,WAuEQ,UAxDV,wBAwDU,UAxDV,yBAwDU,UAxDV,gBAwDU,UAxDV,yBAwDU,UAxDV,yBAwDU,UAxDV,gBAwDU,UAxDV,yBAwDU,UAxDV,yBAwDU,UAxDV,gBAwDU,WAxDV,yBAwDU,WAxDV,yBAmEM,WAEE,gBAGF,WAEE,gBAPF,WAEE,sBAGF,WAEE,sBAPF,WAEE,qBAGF,WAEE,qBAPF,WAEE,mBAGF,WAEE,mBAPF,WAEE,qBAGF,WAEE,qBAPF,WAEE,mBAGF,WAEE,mBAPF,WAEE,qBAGF,WAEE,qBAPF,WAEE,mBAGF,WAEE,mBF1DN,wBEUE,QACE,YAGF,oBApCJ,cACA,WAcA,iBACE,cACA,WAFF,iBACE,cACA,UAFF,iBACE,cACA,qBAFF,iBACE,cACA,UAFF,iBACE,cACA,UAFF,iBACE,cACA,qBA+BE,aAhDJ,cACA,WAqDQ,UAhEN,cACA,kBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,WAuEQ,aAxDV,cAwDU,aAxDV,wBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,aAxDV,yBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,aAxDV,yBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,cAxDV,yBAwDU,cAxDV,yBAmEM,iBAEE,gBAGF,iBAEE,gBAPF,iBAEE,sBAGF,iBAEE,sBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,mBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,mBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,oBF1DN,wBEUE,QACE,YAGF,oBApCJ,cACA,WAcA,iBACE,cACA,WAFF,iBACE,cACA,UAFF,iBACE,cACA,qBAFF,iBACE,cACA,UAFF,iBACE,cACA,UAFF,iBACE,cACA,qBA+BE,aAhDJ,cACA,WAqDQ,UAhEN,cACA,kBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,WAuEQ,aAxDV,cAwDU,aAxDV,wBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,aAxDV,yBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,aAxDV,yBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,cAxDV,yBAwDU,cAxDV,yBAmEM,iBAEE,gBAGF,iBAEE,gBAPF,iBAEE,sBAGF,iBAEE,sBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,mBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,mBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,oBF1DN,wBEUE,QACE,YAGF,oBApCJ,cACA,WAcA,iBACE,cACA,WAFF,iBACE,cACA,UAFF,iBACE,cACA,qBAFF,iBACE,cACA,UAFF,iBACE,cACA,UAFF,iBACE,cACA,qBA+BE,aAhDJ,cACA,WAqDQ,UAhEN,cACA,kBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,WAuEQ,aAxDV,cAwDU,aAxDV,wBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,aAxDV,yBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,aAxDV,yBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,cAxDV,yBAwDU,cAxDV,yBAmEM,iBAEE,gBAGF,iBAEE,gBAPF,iBAEE,sBAGF,iBAEE,sBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,mBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,mBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,oBF1DN,yBEUE,QACE,YAGF,oBApCJ,cACA,WAcA,iBACE,cACA,WAFF,iBACE,cACA,UAFF,iBACE,cACA,qBAFF,iBACE,cACA,UAFF,iBACE,cACA,UAFF,iBACE,cACA,qBA+BE,aAhDJ,cACA,WAqDQ,UAhEN,cACA,kBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,mBA+DM,UAhEN,cACA,UA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,WAuEQ,aAxDV,cAwDU,aAxDV,wBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,aAxDV,yBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,aAxDV,yBAwDU,aAxDV,yBAwDU,aAxDV,gBAwDU,cAxDV,yBAwDU,cAxDV,yBAmEM,iBAEE,gBAGF,iBAEE,gBAPF,iBAEE,sBAGF,iBAEE,sBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,mBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,mBAPF,iBAEE,qBAGF,iBAEE,qBAPF,iBAEE,mBAGF,iBAEE,oBF1DN,yBEUE,SACE,YAGF,qBApCJ,cACA,WAcA,kBACE,cACA,WAFF,kBACE,cACA,UAFF,kBACE,cACA,qBAFF,kBACE,cACA,UAFF,kBACE,cACA,UAFF,kBACE,cACA,qBA+BE,cAhDJ,cACA,WAqDQ,WAhEN,cACA,kBA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,UA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,UA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,mBA+DM,WAhEN,cACA,UA+DM,YAhEN,cACA,mBA+DM,YAhEN,cACA,mBA+DM,YAhEN,cACA,WAuEQ,cAxDV,cAwDU,cAxDV,wBAwDU,cAxDV,yBAwDU,cAxDV,gBAwDU,cAxDV,yBAwDU,cAxDV,yBAwDU,cAxDV,gBAwDU,cAxDV,yBAwDU,cAxDV,yBAwDU,cAxDV,gBAwDU,eAxDV,yBAwDU,eAxDV,yBAmEM,mBAEE,gBAGF,mBAEE,gBAPF,mBAEE,sBAGF,mBAEE,sBAPF,mBAEE,qBAGF,mBAEE,qBAPF,mBAEE,mBAGF,mBAEE,mBAPF,mBAEE,qBAGF,mBAEE,qBAPF,mBAEE,mBAGF,mBAEE,mBAPF,mBAEE,qBAGF,mBAEE,qBAPF,mBAEE,mBAGF,mBAEE,oBCrHV,OAEE,8BACA,2BACA,+BACA,4BAEA,sCACA,0BACA,+CACA,iCACA,8CACA,8BACA,6CACA,oCACA,4CACA,qCAKA,0CAFA,kBXZO,CWaP,kBZksB4B,CYpsB5B,UAGA,CAOA,yBAIE,oCACA,0CZ0sB0B,CYzsB1B,yGAHA,mFAFA,cAKA,CAGF,aACE,uBAGF,aACE,sBAIJ,qBACE,gDAOF,aACE,iBAUA,4BACE,cAeF,gCACE,sCAGA,kCACE,sCAOJ,oCACE,sBAGF,qCACE,mBAkBF,kGACE,oDACA,8CAQJ,cACE,oDACA,8CAQA,8BACE,mDACA,6CC5IF,eAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BAGA,CAlBF,gCAkBE,0CADA,2BACA,CAlBF,iBAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BAGA,CAlBF,eAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BAGA,CAlBF,2BAkBE,0CADA,2BACA,CAlBF,YAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BAGA,CAlBF,eAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BAGA,CAlBF,6BAkBE,0CADA,2BACA,CAlBF,cAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BAGA,CAlBF,aAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BAGA,CAlBF,yBAkBE,0CADA,2BACA,CAlBF,YAOE,sBACA,sBACA,gCACA,8BACA,8BACA,6BACA,6BACA,4BACA,2BAGA,CDiJA,kBAEE,iCADA,eACA,CH3FF,2BGyFA,qBAEE,iCADA,eACA,EH3FF,2BGyFA,qBAEE,iCADA,eACA,EH3FF,2BGyFA,qBAEE,iCADA,eACA,EH3FF,4BGyFA,qBAEE,iCADA,eACA,EH3FF,4BGyFA,sBAEE,iCADA,eACA,EEnKN,YACE,mBdi2BsC,Ccx1BxC,gBhBiRM,iBALI,CgBrQR,gBAJA,gBADA,iCADA,6BbiHiB,CavGnB,mBhBsQM,kBgBpQJ,iCADA,6BhBgQQ,CgB3PV,mBhBgQM,iBgB9PJ,iCADA,6BhB0PQ,CiBtRV,WAKE,gCjBsRI,aALI,CiBrRR,iBf61BsC,CgB91BxC,cAYE,6DAFA,4BADA,qBfwDS,CetDT,wCdIE,sCcPF,afgES,CevET,clB0RI,iBALI,CkBhRR,efPmB,CeQnB,efqHiB,CezHjB,qBCSI,qEDVJ,UAgBA,CCFI,sCDhBN,cCiBQ,iBDGN,yBACE,gBAEA,wDACE,eAKJ,oBAEE,qBfkCO,CejCP,oBhBw2BoC,CgBl2BlC,6CARF,af0CO,CevCP,ShBuhBoB,CgB9gBtB,2CAYE,aAKA,QAAO,CAXP,cAWA,CAKF,qCACE,cACA,UAIF,gCACE,+BhBs0BoC,CgBp0BpC,UAHF,2BACE,+BhBs0BoC,CgBp0BpC,UAQF,uBAEE,wBfhBO,CemBP,UAIF,0CAGE,yBfoGkB,CiBjMpB,sClB+hCgC,CgB57B9B,eAFA,qBAGA,2BfqBW,CepBX,gBAPA,aftBO,CeoBP,sBACA,wBfoGkB,CetGlB,qBAKA,oBCpFE,qID0FF,CC1FE,6HD0FF,CAZF,oCAGE,yBfoGkB,CiBjMpB,sClB+hCgC,CgB57B9B,eAFA,qBAGA,2BfqBW,CepBX,gBAPA,aftBO,CeoBP,sBACA,wBfoGkB,CetGlB,qBAKA,oBCpFE,6HD0FF,CCtFE,sCD0EJ,0CCzEM,wCDyEN,oCCzEM,iBDwFN,+EACE,uChBs7B8B,CgBv7BhC,yEACE,uChBs7B8B,CgB76BlC,wBAOE,6BAEA,4CAHA,0BhBqxBsC,CgB1xBtC,cAIA,efIiB,CeLjB,gBADA,gBADA,UAOA,CAEA,8BACE,UAGF,gFAGE,cAAa,CADb,eACA,CAWJ,iBdjII,yCJ4QE,gBALI,CkBrIR,+BfsDgB,CerDhB,mBdnIE,CcuIF,6CAGE,wBhB+nB0B,CgBhoB1B,qBACA,wBAFA,mBhBioB0B,CgBloB5B,uCAGE,wBhB+nB0B,CgBhoB1B,qBACA,wBAFA,mBhBioB0B,CgB3nB9B,iBd9II,yCJ4QE,iBALI,CkBxHR,gCf2CgB,Ce1ChB,kBdhJE,CcoJF,6CAGE,uBhBsnB0B,CgBvnB1B,oBACA,uBAFA,kBhBwnB0B,CgBznB5B,uCAGE,uBhBsnB0B,CgBvnB1B,oBACA,uBAFA,kBhBwnB0B,CgB9mB5B,sBACE,gCfsBW,CenBb,yBACE,+BfoBc,CejBhB,yBACE,gCfkBc,CeblB,oBAEE,4BfOa,CeNb,cAFA,UfCoB,CeGpB,mDACE,eAGF,uCACE,mBdvLA,sCc2LF,0CACE,mBd5LA,sCcgMF,+DfRgB,CeShB,gEfPgB,CkBxMlB,aACE,qQAWA,CAiBI,uBAIJ,sCAjBA,sBjBFE,gFeHE,CESJ,wCFTI,2BESJ,0BFLI,wCEfN,sCAsBE,CANA,cAJA,aACA,CACA,iCACA,gBACA,CAHA,kCACA,CAQA,oEAOI,CAhBJ,UAoBA,uCAGE,aAGF,oCAEE,oBAKF,4CAEE,CAFF,SAEE,2DnBuuB0B,qBEzwB1B,CiBwCF,oBjBxCE,+CiB8CJ,6BAEE,iBACA,0BrB2NI,iBsBzRJ,wCAEA,CAHA,gBACA,ClBaE,oBkBfJ,mBACE,ClBcE,iBkBXF,iBASA,wCAEA,CAHF,iBACE,CAPA,oBAEE,kBAIJ,CANE,iBASA,aAEA,cAEE,qBACA,CAHF,oBACE,mBAEA,CAIJ,8BACE,6BAGA,qBAGA,eAFA,mBACA,CACA,uDACA,YACA,cADA,mBACA,CACA,yCACA,CAmBA,kBACE,CAVF,uBAKA,qCACE,CAfF,wCACA,+CACA,4EAGA,2DAIA,CATA,4BAeE,gCAGF,CACE,wBAEA,CArBF,mBADA,SAsBE,kCAGF,kDnBNQ,iBmBQN,0BAEA,sBAII,0FAIJ,CAJI,SAIJ,2BAII,6CAKN,8SAsBI,uCAgBJ,4KAOE,gDHlHE,8PGiIE,ClB9HJ,wBeHE,qBGiIE,uCAKN,YALM,mBAKN,4FAIE,cACE,YACA,+BAKN,gCAEE,oLC5JA,CAEA,yCAEA,yCAHA,kBACA,CAEA,+CAOE,CAXF,SAWE,uFrBwgCuC,qCqBvgCvC,0KAUA,wCJbE,wKfGF,wBeHE,iCImBF,qFATF,aJLM,CIKN,mBJLM,wCIgBJ,8BHjCF,kBGsCA,qBALE,iBAKF,oDAKE,wBAFA,mBAEA,aAMF,uBrB+9ByC,CqB79BvC,oBHnDF,iBhBeE,4BmBkCF,CALE,uBnB7BA,CmB4BA,UpBWM,CoBDN,kBnBtCA,UeHE,wOI6CF,2CnB1CA,uBmB4DF,gBACE,CJ3DI,oDI0CJ,CJ3CE,WIiCJ,mBJhCM,CI0CJ,8GAQA,uGnBpDA,CeCE,UI4DF,uCAEA,kCACE,uBAGF,2DACE,wBCvFN,4CAGE,sCAGE,yBACA,mBACA,CALF,wDAKE,+BLYE,oCKTJ,CAEE,wBAGA,SACA,mBACA,CANA,WACA,CAKA,2GAKA,uGLHE,CKTJ,iDAiBA,kEAEE,uCAEA,2JACE,eADF,wBACE,sBAGF,iJtBwgCoC,0CsBxgCpC,4JAEE,gBACA,CAHF,6BAGE,sBASJ,4BAEE,CARA,uDAMF,oBANE,kCACE,uBACA,CAFF,MAQA,oBAOA,8DAbE,kBAIJ,CANE,SAgBE,4DACA,qFAFF,6IACE,+FACA,iBAEA,+HrBQK,sBClDP,CoBgDI,oBpBhDJ,qMoB0CE,sBACE,CADF,oBACE,uGAYJ,uBpBvDA,oBoBuDA,6BACE,sBACA,CADA,oBACA,iEAKF,wCACE,2DAOF,oLC9EF,mGAUA,uEAUE,qBAGA,sCAYJ,CAfI,WADF,YACE,CAVF,kBAGE,CAHF,kBAGE,UAsBJ,4MrB3BI,qBqBgDJ,uCrBhDI,wBqBsCF,kBrBtCE,CqBsCF,kBrBtCE,UqBgDJ,6FrBhDI,2DqByDJ,gGAIE,mDrB7DE,wBqBkEJ,cAEE,mBAaE,CAfJ,2BAEE,CAFF,kBAeI,uWrBhEA,mBqByEA,mIrB1EA,YACA,CqByEA,kDrBzEA,oBqByEA,wJAaF,yCrBtFE,iBqBsFF,CrBvFE,kBqBuFF,mHrBxEE,wCqB6EF,CrB9EE,gBACA,CqByEA,mBAIF,gFrB5GE,iqBsBmDI,4BACA,CADA,yBACA,2IA6BF,2BACE,CADF,yBxBw+BmB,gBwBv+BjB,wHA/FJ,2BA+GE,CA/GF,wBA+GE,iBAEA,iCAFA,aAEA,gCAFA,UAEA,gBAKE,wEAGF,CAHE,WAJA,YAGF,iBACE,CADF,qDAHE,oCAOF,+HAMF,wEA/HA,iUA0IM,qE1B+IJ,8C0B5PJ,uDAIE,mE1BwPE,8C0BjPF,mFAKA,0HA7CA,6JA+DE,gCACE,sDACA,mWAjEJ,4BA+GE,kCAEA,CAjHF,aA+GE,gCA/GF,UAiHE,kBAIA,uEACE,CADF,WAJA,YACE,CAGF,iBAHE,gBAGF,CAHE,oCADF,oCAKE,+IAIA,4EAKJ,gDA/HA,uWCAF,gDAEA,sDAEA,uEAGA,gDAEA,uFAEA,6CACA,mFACA,qDACA,2GAIA,kCACA,wD3BsQI,gB2BpQJ,+VPvBA,SDYI,sIAIA,yBQhBN,oCAuCI,kDAEA,iFAIF,wCAEE,iFAKF,+BACE,+EAEA,CAiCA,kCAHA,kEAGA,2CAZF,0BAKE,eA1BA,oBACA,CASF,qCACE,kCACA,CAKE,2EAIJ,CAfI,uDAIJ,CAWA,kBAYE,6HAUF,CAjBE,wBACA,uCAGA,CATF,qBAsBA,uCAGE,iCAEA,uCAEA,8CACA,CAJA,+BACA,qBAGA,uBCzFF,iCACA,wCACA,CAHA,yBAGA,CACA,mBACA,uCACA,8CAEA,0CACA,CALA,+BACA,CAGA,SACA,+BACA,6CACA,0CACA,CADA,SACA,oGATA,wCACA,+CACA,CAHA,gCAGA,0KAKA,yCACA,oDAXA,0CAEA,iDACA,CAUA,mCAVA,sCAEA,CDiGA,mBCjGA,iCACA,kDACA,0BACA,0BACA,oCACA,gEACA,2BACA,qCACA,wDAZA,6BACA,6BAEA,uCACA,mCACA,kDACA,0BACA,0BACA,oCACA,sCACA,4BACA,+DACA,wDAXA,6BACA,6BACA,uCACA,iCACA,kDACA,0BACA,0BACA,oCACA,qCACA,4BACA,+DACA,wDAXA,6BACA,6BACA,uCACA,8BACA,kDACA,0BACA,0BACA,oCACA,qCACA,sDACA,qCACA,wDAXA,6BACA,6BACA,uCACA,iCACA,kDACA,0BACA,0BACA,oCACA,qCACA,4BACA,+DACA,wDAXA,6BACA,6BACA,uCACA,gCACA,kDAEA,0BACA,8DACA,oCACA,2BACA,2BACA,6FAaA,6BACA,6BACA,uCACA,YACA,uCACA,8BACA,0BACA,8DACA,sCACA,2BACA,2BACA,qCACA,wDAbA,6BACA,6BACA,uCAEA,8BACA,kDACA,0BACA,0BACA,oCACA,8DACA,2BACA,qCACA,wDD2FA,6BCtGA,6BACA,uCACA,sBACA,oDACA,0BACA,0BACA,oCACA,qCACA,sDACA,qCACA,wDAEA,gCAbA,iCACA,uCACA,mBACA,wBACA,uBACA,uDACA,0BACA,oCACA,sCACA,sDACA,qCACA,wDAEA,gCAbA,iCACA,uCACA,CACA,wCACA,sBACA,wDACA,0BACA,oCACA,qCACA,sDACA,qCACA,wDAEA,gCAbA,iCACA,uCAEA,sCACA,oDACA,0BACA,0BACA,oCACA,qCACA,sDACA,qCACA,wDAEA,gCAbA,iCACA,uCAEA,yCACA,sBACA,wDACA,0BACA,oCACA,qCACA,sDACA,qCACA,wDAEA,gCAbA,iCACA,uCAEA,wCACA,sBACA,wDACA,0BACA,oCACA,oCACA,sDACA,qCACA,wDAEA,gCDuGA,iCACA,uCAEA,uCACA,oDACA,0BACA,8DACA,sCACA,2BACA,gEAEA,wDAOA,iEAKA,uCACE,mBAGF,mBACE,sBAWJ,8BCxIE,0BACA,0B5B8NI,oC4B5NJ,mCDyIF,2BC5IE,2BACA,qC5B8NI,wD6B/RN,gCVoBM,wEUjBJ,mBAOA,mCAKF,oCVCM,wBAIA,kCULN,gDAKE,wCVJI,iDAIA,yCACE,gCWpBR,0EASA,qCCyBI,qBACE,yCAEA,yBApCJ,yBACA,0CACA,+BACA,4BA0DE,yBACE,wBD5CJ,4BACA,kDAEA,qDACA,0B9BuQI,2B8BrQJ,kDACA,OACA,qEACA,iDACA,+CACA,2FACA,6DACA,sCACA,CADA,OACA,uEACA,uFAEA,qDACA,wBAGA,eACA,mCACA,CAHA,mCACA,CADA,iCADA,kBACA,sBAGA,8BACA,6BACA,uDAIA,yDAEA,8BACA,0E9B2OI,oF8BtOJ,oDAEA,kDACA,yF1BzCE,4D0BgDA,sCAwBA,yDAGE,8CAOA,oDAEA,CACE,iDAbJ,qCAGE,gGAOA,CAEA,sEnBxCF,mCmB6BE,oCAEA,sCAEE,CASA,2BnB1CJ,CmBwCE,sCAEE,CnB1CJ,4EmB+BE,+CAMF,CALI,8BAMF,CAEA,aATA,sCACE,CAMF,eAEA,CATA,SASA,sCnBxCF,kEmB+BE,CAMF,kBACE,gBADF,iEAGE,2CnBxCF,CmBwCE,QnBxCF,sBmB4BA,yDAGE,qCACE,iBAKJ,oCAGE,0CACE,wBAWN,4DAIE,OADA,UACA,wCCpFA,uCAEE,SACA,CADA,OACA,0BACA,wBA7BJ,4DAEA,wCAmDE,wDDiEF,2CAEE,wBAGA,4DCjGE,OADF,UACE,uBACA,iBACA,uCAtBJ,4CAEA,2CACA,yCA6CI,OADF,UACE,uBD0EF,iBACE,uCAMJ,iBACE,2BAGA,yBACA,6DClHE,yCAEA,kB7B2hBwB,uC6B9gBtB,UADF,OACE,yCAIA,WACA,wCAEA,CAFA,aADA,QAhCN,gCDoIE,2DAQJ,CCpGI,mCD4FA,CC5FA,aAtCF,UAsCE,CAxCF,oBACA,mBACA,sBD0IF,sCAEE,sDAEA,UACA,qCAQA,CATA,YACA,CADA,gBASA,iCAKA,oCAGA,uBACA,CALA,cACA,CAFA,iCACA,CADA,WAFA,6DAQA,uC1BtKE,8C0ByKF,gBAEE,2CACA,SV3LF,uCU+LA,CV/LA,aU2LE,gBAIF,mCAGE,UVlMF,CUiME,qBVjMF,YUsMA,CALE,wCVjMF,CUsMA,mCAWA,oCAMA,CAPF,uBACE,CAPE,iCAMJ,CAPI,UACA,CAJF,oBAEE,0CAeF,0F9BoEI,gBALI,C8B5DR,kBAMA,kDACA,CAPA,qDAKF,CAEE,UAFF,eAEE,gBAUA,4BACA,SACA,sDACA,CALA,WACA,oCATA,aACA,CAOA,eACA,CARA,2EAOA,CACA,kBACA,oBATA,UAYA,2CAEA,iDACA,CAFA,8DAEA,6CEpPF,kDAGE,CFkPA,0CACA,qBEnPA,iDAUA,6BAPA,4CAEE,oBAKF,gQAWF,mCAGE,CAdA,yFAcA,gDAGE,yB5BZA,8D4BoBF,4DAEE,oCAIF,iJ5BVE,qC4BoBF,6G5BNE,mBACA,CADA,kBACA,qB4BwBJ,0CAIE,cAFA,iBAEA,mXAkBA,uBAoBF,2BACE,2BACA,2BACA,UAEA,kDAEE,qFAGF,gBAEE,sJ5BrFA,4B4B8FF,C5B/FE,yB4B+FF,8GClIA,2BACA,C7BqBE,wB6BrBF,wBAEA,qBACA,CAHA,sBAGA,yGAEA,uDAIA,cACA,0EAOA,oHAGA,oBADA,oBACA,qBdZI,uBccJ,qBddI,gFAIA,uGcgBF,eACA,sHAUA,2BACA,CADA,4BAEA,qFAWF,kDACA,iEACA,0HACA,uDACA,aACA,yEAOE,yBADA,8BACA,CAPF,cAGA,sCAEA,2CACE,CANF,iEAGA,CAIE,iG7B3CA,iD6B8CA,iDAGE,oCACA,8CAGF,4CAEE,CAFF,SAEE,2DACA,CACA,cAIJ,CALI,mBAKJ,4DAEE,kDACA,oDACA,2GAKA,yD7BtEA,+C6BmFF,uGAKA,oFAGE,qBAEE,wDAKJ,wDAEE,yDb7HF,CaqHI,sDbrHJ,qDa0IA,uDACA,CAFA,iBAEA,2DAME,4BAEA,0BALF,uCAKE,+DAIE,kDAIJ,yDAEE,CARA,0CAQA,0BAEA,wBAUF,0BAEE,CAbA,mDAaA,YACA,oDAKF,sCAIE,sCAMF,qEAWA,+BCnMA,4BACA,0BDqMA,uCCrMA,CACA,uDACA,mDACA,CAFA,2CAEA,2CACA,uGAEA,gCACA,0BACA,oEACA,CADA,eADA,eAEA,+DACA,gCACA,gEAGA,iCADA,+CACA,CADA,eACA,yCACA,kFACA,wBACA,oFACA,kCAIA,aACA,qBACA,aACA,uCACA,yFAMA,iIAGE,8DAsBF,qCACA,sEACA,6DAEA,mEAIA,sCAEE,sCACA,sCAWF,uCAGA,qRAaE,yEAMF,0DAWA,uCAEA,4DAEA,CAGE,kBAaJ,CAbI,4BAaJ,6BAKE,8DAKA,CA1BA,iBA0BA,qIAIA,kBACA,CAFA,YACA,mBACA,4CftIM,kCe2IJ,Cf5IE,0CACE,CALF,8CAIA,CfDF,+CeHE,Ce2IJ,4C9BxIE,C8B6IA,kBAGF,yCAEE,wCACA,sBAMJ,qCACE,+BAGA,4BACA,2CACA,uDAEA,6DAIA,mCvBzHE,CuBsIA,eAEI,CvBxIJ,euBsIA,CvBtIA,cuBwII,yDAGA,mCAGE,2CACE,cAIA,6BADF,sCACE,0DACA,mCAIJ,kBAIA,mBAHE,eAGF,6BAeE,4BACA,0EAEA,qDAMA,CAXA,4BAEA,CANF,4CAGE,cACA,CAZA,6EAQF,CAeE,8CACE,uCAGF,+BAEE,wBAEA,oBvB5LR,uBuBwII,qDAGA,CAHA,UAFJ,oBAKI,sBAOE,iDACE,wBACA,CADA,4BACA,qBARF,oBAEA,mCAIA,CAJA,WAME,oBAIJ,uCACE,gBAGF,yBACE,kCACA,2BAGF,+BAIA,gEAIE,iBACA,yCAGA,gDAEA,CAHA,iDAGA,sCAKA,oDAIA,sCACE,mCAGA,0CA9CF,yDAIA,CAVF,WACA,sBAEA,CvB3IJ,euBsIA,CAYM,wCACE,CARJ,4BAGE,CALF,qBAHJ,YAaQ,gDACA,0DAIJ,yBACE,kBAGF,CAJA,SAIA,0BACE,kCACA,2BAGF,+BAIA,gEAIE,iBACA,yCAGA,gDAEA,CAHA,iDAGA,sCAKA,oDAIA,sCACE,mCAGA,0CA9CF,yDACE,CAPJ,WACA,sBAEA,CvB3IJ,euBsIA,CAYM,wCACE,CARJ,4BAGE,CALF,qBAHJ,YAaQ,4DACA,8CAIJ,yBACE,kBAGF,CAJA,SAIA,4CACE,gBACA,2BAGF,+BAIA,gEAIE,iBACA,yCAGA,gDAEA,CAHA,iDAGA,sCAKA,oDAIA,uCACE,kCAEA,YACA,8BAhDF,sCAEA,mBACE,CAPJ,YACA,qBAEA,CvB3IJ,euBsIA,CASQ,wBAGF,iBAPF,4BACE,CAHF,qBAHJ,YAYM,gDACE,YACA,8CAIJ,yBACE,kBAGF,CAJA,SAIA,6CACE,gBACA,2BAGF,+BACE,kBAGF,8CAIE,iBACA,yCAGA,gDAEA,CAHA,iDAGA,sCAKA,oDACE,sBAGF,mDAEE,YAEA,8BA9CF,sCAIA,oBATF,YAEA,qBACE,CANN,eAEI,CAUE,wBACE,iBAPF,4BAEA,CALF,oBAEA,CAHA,YAWI,gDACA,0DAKF,wBAGF,mBACE,CADF,SACE,2BACA,mBAGF,2CAIA,gCAGE,kBACA,+CAEA,iBACA,0CAGA,gDAKA,CAPA,iDAOA,uCAIA,qDACE,sBAGA,oDAiBZ,YAGE,+BAGA,sCACA,mBACA,CAJA,YACA,sBAFA,eACA,CAIA,wCACA,CAJA,4BACA,CAFA,oBACA,CADA,YAKA,6DACA,4DClRF,WAEE,mBACA,CADA,SACA,iCACA,2BACA,4BACA,6DAEA,uDAGA,gDAEA,CAJA,iDAIA,mCACA,iDAEA,sBACA,gBACA,gCACA,aACA,0BAQA,sCAEA,oBANA,WACA,CACA,qBACA,CAPA,eAGA,CAOA,wBACA,gBACA,CALA,4BACA,CAHA,oBACA,CAHA,YASA,yD/BjBE,2C+BqBF,YACE,YACA,mBAGF,CAHE,SAGF,0CAEE,qCAGE,4C/BtBF,+CACA,8B+ByBA,6BACE,mC/BbF,oDACA,wR+BmCF,iCAIA,2DAEA,yDAIA,yBAIA,mCAIA,gCACE,uBAQJ,sCACE,6DAEA,sBACA,yCACA,uEAEA,4B/B7FE,C+BmGF,qBACA,0BACA,CAFA,kCACA,CACA,oEACA,2CAEA,CALA,2B/BnGE,+D+BmGF,C/BnGE,6B+BwGF,U/BxGE,oE+BkHJ,C/BlHI,kB+BkHJ,+BACE,yDACA,2DACA,CAFA,kBAEA,8BAKE,4DAKF,CAPA,6DAEE,CAFF,qBAOA,+DACA,wBAUA,2BANF,aACE,wDAKA,aAIF,iC/B9II,2C+B8IJ,gB/BrII,mCACA,C+BuIF,kD/BvIE,sCADA,eACA,kB+B+IJ,oB/BlII,0DACA,cOoBA,sCwB+HA,4EASE,CAbF,8BxB3HA,CwB2HA,gB/B/IA,iE+B4JE,0BACE,uF/BpKJ,c+B6KM,sCAKA,0EALA,+B/B7KN,iE+BkLM,yBAGE,uF/BvKR,mB+BmLM,gBALA,sGAKA,C/BnLN,mD+BmLM,sEAGE,sCCjOV,oBAGA,kDACA,CAHA,mDAIA,mBAIA,gDpC+QI,CoChRJ,0DACA,CAJA,yBAGA,CAHA,KpCmRI,0CoC7QJ,UACA,yBACA,yDAGA,2DhCJE,4BiCbF,4DACA,CDsBE,6DCtBF,mBACA,kEACA,2CACA,8CACA,yBACA,cADA,aACA,oCACA,4BACA,CADA,yBACA,kGACA,yBACA,qGAEA,iEACA,2BACA,CADA,wBACA,oGAKA,wBACA,uGAQF,2BACE,CACA,mCAEA,oDAEE,2BACA,sBASJ,iDAEE,CAaE,2CACA,CALA,4BATF,oBACA,CAGA,mCAGE,wCACA,cACA,CARF,2DAGA,CAKE,kBAGF,uBACE,CAJA,kBAKA,wDAQJ,sBACE,0CAEA,yFACA,oDAGA,0FAEA,sCjCvDE,uDiC2DF,4DjC5CE,sDiCgDF,yDAGE,wDACA,yDAMA,8CACA,kCACA,8EAKA,CAGE,gDACA,CAJF,kCAEA,gBACE,CADF,cAEE,2CAaF,CAbE,oBAaF,8CAII,4DjCvDJ,gEiC4DI,oBjC5DJ,UiC4DI,6DAKA,qDACE,CjC9EN,6CAYA,qBiCiEI,CjC7EJ,SiC8EM,gCAIA,sDACA,CAFF,8CAIE,kBAEE,wCAxBR,iFAII,CAoBI,iCAFF,6FAEE,CAFF,iBAlBF,8BjCvDJ,8DAZA,6BiCwEI,iCjCxEJ,CiCwEI,kCjCxEJ,qDiC6EI,iDAIA,CjCjFJ,yCiC6EI,oBAIA,yBACE,+CAGA,uDAJF,uCACE,CADF,SAIE,mCACE,4DAvBR,kDAII,CAoBI,qDApBJ,gHjCvDJ,4DiC4DI,gGAKA,4BjC7EJ,0DiC8EM,gDAGF,sEAIE,oBAHA,kDAGA,iEAEE,mDAxBR,CAuBQ,sDAvBR,yBACE,0BAGE,2FjCvDJ,4DiC4DI,mGAKA,4BjC7EJ,0DiC6EI,mDAIA,yEAIE,oBAHA,kDAGA,oEAEE,mDAxBR,CAuBQ,sDAvBR,0BACE,0BAGE,2FjCvDJ,4DiC4DI,mGAKA,4BjC7EJ,0DiC6EI,mDAIA,yEAIE,oBAHA,kDAGA,oEAEE,mDAxBR,CAuBQ,sDAvBR,0BACE,0BAGE,2FjCvDJ,4DiC4DI,mGAKA,4BjC7EJ,0DiC6EI,mDAIA,yEAIE,oBAHA,kDAGA,oEAEE,mDjClIR,CiCiIQ,sDjCjIR,2BiCmJF,0BACE,2FAGE,4DAcF,mGAGA,4BADA,0DACA,mDACA,yEAEA,oBADA,kDACA,oEAEA,mDACA,CAFA,sDAEA,sDAVF,kBACE,0EACA,4DACA,0BACA,0EAEA,4BADA,0DACA,oDACA,0EAEA,oBADA,kDACA,qEARA,mDACA,CAQA,sDARA,uEACA,kDACA,+CACA,oGACA,+CACA,6DACA,4DACA,gEACA,6DATA,iEACA,yDACA,0DACA,oEACA,4BACA,wGACA,+DACA,4DACA,kEACA,CAVF,4DACE,mEAEA,4DACA,4DACA,qEACA,+EACA,+CACA,6DACA,4DACA,gEAVF,6DACE,iEAEA,yDACA,0DACA,oEACA,yEACA,4CACA,0DACA,4DACA,6DAVF,6DACE,8DAEA,uDACA,uHACA,0BACA,qDACA,gDACA,4DACA,4DACA,gEAVF,6DACE,iEAEA,yDACA,0DACA,oEACA,6EACA,8CACA,4DACA,4DACA,+DC1LF,6DACA,gEAEA,wDACA,yDAEA,mEACA,2EAMA,6CACA,2DACA,4DAGA,8DAIE,6DAEA,+DAKA,uDACA,wDAKA,kEACA,uBACA,kDAKF,4CC/CA,0DCEE,4DACA,6DADA,6DACA,4OAFF,qCAEE,iZCFA,+DACA,4IAMI,uEACA,8BAPJ,kCAOI,oCAPJ,aAGE,CAHF,SAOI,6BAJF,+BALJ,CASM,0DATN,CASM,oBATN,kBACE,qFACA,CADA,SACA,2LAGE,wDAGE,qDACA,8KARJ,CAQI,oBARJ,oFACA,CADA,oBACA,0LAMI,CANJ,oBAMI,iBACA,kEADA,oBACA,wGATN,eACE,gEACA,CADA,oBACA,0LAGE,+FAIE,8LARJ,oFACA,gMAGE,iGAIE,gMARJ,mFACA,0LAMI,+FACA,8LARJ,oFACA,iLAMI,4FACA,wLARJ,oFACA,0LAMI,+FACA,8LAQN,oFACA,uLAGE,8FAEE,uCACA,sOCvBJ,oLCCA,4LACA,CzC2c4B,4FyCtc1B,qFxBUE,6EwBAF,oGClBJ,4FAMI,mCAKA,uDAUA,0FADF,kFCfA,qBAGF,2EAcM,8GAOA,sGALA,qDAIA,6EANA,gHAOA,wGALA,mBAIA,8IALA,CAIF,SAJE,YAKA,mBACA,kCC7BJ,2BAEA,CDsBI,mBAGF,YACE,uFANA,+EAMA,4BC1BJ,gBAKA,iBACA,CANA,aACA,CAGF,UACE,CACA,oCAEA,CAPA,SAOA,uCCRF,8FCME,8DAGA,oCACA,eAIA,WAJA,aACA,mCAGA,2EACE,sBCdF,iCACE,aAEA,wBAGA,aACA,gCCRJ,YCCE,KCJA,0BDKA,MCNF,CDKE,eACA,QCNF,YAKE,CAJA,cAEA,QAEA,aCoEU,iCDpEV,uBC6DM,gBAOI,6BAPJ,QAOI,yBAPJ,oBAOI,yEAPJ,cAOI,0BAPJ,0BAOI,sDAPJ,uBAOI,iGAPJ,KAOI,0EAPJ,YAOI,mBAPJ,QAOI,wGAPJ,gBAOI,mEAPJ,gBAOI,KAPJ,oCAOI,uBAPJ,gBAOI,cAPJ,mBAOI,kBAPJ,kBAOI,mBAPJ,iBAOI,kBAPJ,CAOI,aAPJ,QAOI,4IAPJ,kBAOI,qBAPJ,CAOI,qBAPJ,0BAOI,CAPJ,mBAOI,6BAPJ,CAOI,mLAPJ,CAOI,UAPJ,OAOI,CAPJ,iBAOI,wBAPJ,gBAOI,+GAPJ,oBAOI,CAPJ,cAOI,uCAPJ,iCAOI,YAPJ,2CAOI,8CAPJ,+BAOI,4IAjBJ,YACE,2EADF,+BACE,6EADF,2BACE,wEADF,mBACE,mEADF,6CACE,yDADF,kCACE,mEADF,cACE,4EADF,yBACE,wFAgBE,+JAPJ,2BAOI,+JAPJ,sCAOI,wBAPJ,iBAOI,wCAPJ,uBAOI,SAPJ,sCAOI,uGAPJ,2CAOI,sKAPJ,YAOI,kHAPJ,2DAOI,wRAPJ,kBAOI,4EAPJ,oBAOI,8QAHI,6FAGJ,kBAHI,yBAGJ,8FAHI,iBAGJ,2EAPJ,0BAIQ,uBAGJ,iEAPJ,kBAIQ,gCAGJ,4DAPJ,WAIQ,mBAGJ,mFAPJ,UAIQ,oBAGJ,gFAPJ,oCAOI,kEAPJ,kHAOI,2IAPJ,aAOI,kIAPJ,gBAOI,uIAPJ,eAOI,0FAPJ,iBAOI,yIAjBJ,mBACE,qBADF,oHACE,4EASF,cAOI,8FAPJ,iBAOI,qBAPJ,4EAOI,gBAPJ,qBAOI,uMAPJ,qBAOI,6GAPJ,0EAOI,oCAPJ,0EAOI,wGAPJ,wDAOI,mGAPJ,2EAOI,sDAPJ,uBAOI,oJAPJ,mDAOI,CAPJ,oCAOI,0EAPJ,WAOI,qCAPJ,0BAOI,oBAPJ,uBAOI,4CAPJ,oBAOI,uBAPJ,oBAOI,6CAPJ,qBAOI,0BAPJ,0BAOI,0BAPJ,4BAOI,6BAPJ,iCAOI,SAPJ,qBAOI,sCAPJ,OAOI,2BAPJ,oBAOI,2BAPJ,6BAOI,8BAPJ,kCAOI,SAPJ,sBAOI,aAPJ,sCAOI,8HAPJ,oCAOI,iGAPJ,aAOI,4EAPJ,uBAOI,CAPJ,WAOI,wBAPJ,CAOI,8sCAPJ,sBAOI,8EAPJ,CAOI,+BAPJ,UAOI,2BAPJ,iBAOI,UAPJ,iBAOI,sDAPJ,CAOI,yIAPJ,CAOI,gCAPJ,uBAOI,kHAPJ,MAOI,mJAPJ,CAOI,4BAPJ,iCAOI,4TAPJ,CAOI,2BAPJ,gCAOI,8BAPJ,iEAOI,6BAPJ,CAOI,0BAPJ,OAOI,6DAPJ,8BAOI,41BAPJ,2BAOI,2NAPJ,8BAOI,iRAPJ,OAOI,6BAPJ,gCAOI,UAPJ,0BAOI,yHAPJ,OAOI,6BAPJ,wBAOI,+SAPJ,gEAOI,khBAPJ,6BAOI,8TAPJ,6BAOI,gMAPJ,+BAOI,sCAPJ,QAOI,sQAPJ,2BAOI,QAPJ,6BAOI,QAPJ,2BAOI,QAPJ,6BAOI,QAPJ,2BAOI,sHAPJ,wBAOI,sFAPJ,OAOI,2bAPJ,MAOI,4BAPJ,4BAOI,iCAPJ,CAOI,6DAPJ,CAOI,iEAPJ,CAOI,2BAPJ,oCAOI,2BAPJ,sCAOI,CAPJ,4BAOI,qCAPJ,0BAOI,uCAPJ,4BAOI,6FAPJ,OAOI,8DAPJ,iCAOI,oCAPJ,gCAOI,0CAPJ,0BAOI,wLAPJ,4BAOI,CAPJ,MAOI,+BAPJ,MAOI,mCAPJ,0BAOI,OAPJ,+BAOI,OAPJ,8BAOI,CAPJ,mCAOI,OAPJ,+BAOI,OAPJ,6BAOI,qXAPJ,QAOI,4BAPJ,mBAOI,sDAPJ,0BAOI,4BAPJ,0BAOI,2CAPJ,wBAOI,mCAPJ,kCAOI,sEAPJ,oCAOI,mGAPJ,eAOI,4DAPJ,eAIQ,+BAGJ,wEAHI,0BAGJ,2EAHI,6CAGJ,yCAPJ,gCAOI,yEAPJ,0BAOI,sEAPJ,2BAIQ,CAGJ,wEAPJ,OAIQ,8BAGJ,wCAHI,wCAGJ,wCAPJ,aAIQ,6BAGJ,yEAHI,qCAGJ,uBAPJ,yBAOI,sEAPJ,CAOI,uBAPJ,yBAIQ,aAGJ,0BAPJ,oCAOI,yCAPJ,uBAIQ,8BAGJ,4BAPJ,mCAIQ,+BAGJ,sCAHI,iBAGJ,mDAPJ,kCAOI,kBAjBJ,gKAUA,gCAOI,kCAPJ,mEAOI,iBAPJ,mBAOI,uGAPJ,kGAOI,mLAjBJ,mBACE,kEADF,aACE,mBAIA,iEALF,YACE,mBAIA,gEAJA,gCAIA,iEAJA,aAIA,oFAYE,+BAKF,sEALE,0EAYE,mCAnBN,+BAOI,gBAKF,sDAOI,sBAnBN,mBAIQ,0CAGJ,+KAPJ,oBAIQ,yCAGJ,kNAHI,2EAGJ,gKAPJ,+CAOI,yMAPJ,CAOI,uLAPJ,qBAIQ,CAGJ,yIAHI,0CAGJ,uHAHI,0CAGJ,iMAPJ,6FAOI,+JAjBJ,+FAMI,yBANJ,6BACE,qGADF,6FAKE,CACE,kDANJ,kGAMI,0FADF,yBACE,+NAWA,qDAPJ,gMAOI,uBAPJ,6BAOI,8LAHI,sBAGJ,iXAPJ,kGAOI,8FAHI,CAJR,kEAOI,gCAPJ,kEAOI,iCAhBF,kGADF,kEAUA,gCAOI,2NAPJ,iBAOI,kMAPJ,iBAOI,wGAPJ,2EAOI,8UAPJ,0EAOI,4BAPJ,0EAOI,uGAPJ,iBAOI,kNAPJ,gFAOI,mDAPJ,oBAOI,uEAPJ,iBAOI,iHAPJ,wDAOI,0EAPJ,iBAOI,6HAPJ,mBAOI,0PAPJ,kBAOI,6NAPJ,kCAOI,kGAPJ,UAOI,0eAPJ,oDAOI,sOAPJ,2DAOI,+LAPJ,gBAOI,wHAPJ,gBAOI,iWAPJ,mBAOI,6HAPJ,aAOI,6DAPJ,yDAOI,uDAPJ,mCAOI,gBAPJ,+DAOI,8ZAPJ,CAOI,4DAPJ,gBAOI,gEAPJ,CAOI,6DAPJ,qBAOI,4dAPJ,8DAOI,8aAPJ,+DAOI,mFAPJ,gEAOI,+DAPJ,+HAOI,kFAPJ,2DAOI,yDAPJ,kBAOI,wJAPJ,4DAOI,kBAPJ,2DAOI,2EAPJ,8DAOI,CAPJ,2DAOI,8JAPJ,+DAOI,6DAPJ,uBAOI,iGAPJ,oKAOI,YAPJ,2BAOI,iCAPJ,yBAOI,4CAPJ,yBAOI,yCAPJ,oBAOI,qCAPJ,mCAOI,wBAPJ,+BAOI,6BAPJ,sBAOI,6BAPJ,2BAOI,qBAPJ,4BAOI,0BAPJ,sBAOI,kCAPJ,gCAOI,qBAPJ,4BAOI,0BAPJ,sCAOI,uFAPJ,iCAOI,oFAPJ,iBAOI,2BAPJ,kBAOI,4BAPJ,kCAOI,mBAPJ,6BAOI,kCAPJ,eAOI,uBAPJ,2DAOI,0KAPJ,sCAOI,yGAPJ,eAOI,qJAPJ,oCAOI,yBAPJ,kCAOI,4BAPJ,gCAOI,kTAPJ,wBAOI,6GAPJ,6BAOI,yBAPJ,kCAOI,6OAPJ,2BAOI,mLAPJ,uBAOI,yGAPJ,4BAOI,gDAPJ,iBAOI,0FAPJ,aAOI,kBAPJ,YAOI,iCAPJ,iBAOI,0FAPJ,SAOI,8BAPJ,uBAOI,4FAPJ,YAOI,qBAPJ,UAOI,yJAPJ,CAOI,4BAPJ,oCAOI,mEAPJ,6BAOI,oCAPJ,CAOI,2BAPJ,UAOI,4BAPJ,CAOI,6BAPJ,oCAOI,oEAPJ,2BAOI,mCAPJ,CAOI,sBAPJ,wCAOI,6BAPJ,sCAOI,qCAPJ,4BAOI,CAPJ,yBAOI,UAPJ,8BAOI,CAPJ,2BAOI,UAPJ,4BAOI,CAPJ,yBAOI,4lBAPJ,4BAOI,sCAPJ,sCAOI,qCAPJ,UAOI,oTAPJ,4BAOI,kDAPJ,4BAOI,aAPJ,4BAOI,UAPJ,uBAOI,+NAPJ,CAOI,mCAPJ,aAOI,0BAPJ,UAOI,kCAPJ,uBAOI,kGAPJ,UAOI,kCAPJ,sBAOI,yCAPJ,8BAOI,2GAPJ,CAOI,4BAPJ,wCAOI,qEAPJ,CAOI,4BAPJ,WAOI,iNAPJ,8BAOI,uCAPJ,6BAOI,sCAPJ,+BAOI,wCAPJ,6BAOI,sCAPJ,+BAOI,wCAPJ,6BAOI,kEAPJ,WAOI,kHAPJ,0BAOI,uCAPJ,WAOI,sMAPJ,4BAOI,8KAPJ,wCAOI,+BAPJ,CAOI,mNAPJ,2BAOI,WAPJ,6BAOI,WAPJ,2BAOI,WAPJ,6BAOI,WAPJ,2BAOI,SAPJ,mBAOI,iEAPJ,SAOI,sBAPJ,SAOI,iCAPJ,sBAOI,oKAPJ,8BAOI,yGAPJ,CAOI,4BAPJ,uCAOI,+BAPJ,qCAOI,qEAPJ,8BAOI,uGAPJ,uBAOI,UAPJ,sEAOI,8BAPJ,CAOI,2BAPJ,UAOI,6BAPJ,CAOI,0BAPJ,yCAOI,6BAPJ,UAOI,8BAPJ,0BAOI,0CAPJ,4BAOI,mGAPJ,CAOI,0EAPJ,UAOI,gEAPJ,UAOI,gEAPJ,UAOI,6DAPJ,wCAOI,uCAPJ,UAOI,oEAPJ,UAOI,oEAPJ,UAOI,4BAPJ,UAOI,0BAPJ,UAOI,+B1CVR,wC0CUQ,gRAPJ,CAOI,0EAPJ,UAOI,kEAPJ,uCAOI,qCAPJ,WAOI,0BAPJ,oBAOI,8BAPJ,WAOI,kBAPJ,WAOI,+BAPJ,kBAOI,+BAPJ,CAOI,4BAPJ,eAOI,kCAPJ,wBAOI,eAPJ,uBAOI,eAPJ,sBAOI,sDAPJ,sBAOI,yHAPJ,uBAOI,gGAPJ,0DAOI,kBAPJ,wDAOI,kBAPJ,gCAOI,8CAPJ,8BAOI,0BAPJ,kDAOI,4BAPJ,kBAOI,8BAPJ,0BAOI,yCAPJ,cAOI,2CAPJ,2BAOI,0CAPJ,oBAOI,qCAPJ,mCAOI,wBAPJ,+BAOI,6BAPJ,sBAOI,6BAPJ,2BAOI,qBAPJ,4BAOI,0BAPJ,sBAOI,kCAPJ,gCAOI,qBAPJ,4BAOI,0BAPJ,sCAOI,uFAPJ,iCAOI,oFAPJ,iBAOI,2BAPJ,kBAOI,4BAPJ,kCAOI,mBAPJ,6BAOI,kCAPJ,eAOI,uBAPJ,2DAOI,0KAPJ,sCAOI,yGAPJ,eAOI,qJAPJ,oCAOI,yBAPJ,kCAOI,4BAPJ,gCAOI,kTAPJ,wBAOI,6GAPJ,6BAOI,yBAPJ,kCAOI,6OAPJ,2BAOI,mLAPJ,uBAOI,yGAPJ,4BAOI,gDAPJ,iBAOI,0FAPJ,aAOI,kBAPJ,YAOI,iCAPJ,iBAOI,0FAPJ,SAOI,8BAPJ,uBAOI,4FAPJ,YAOI,qBAPJ,UAOI,yJAPJ,CAOI,4BAPJ,oCAOI,mEAPJ,6BAOI,oCAPJ,CAOI,2BAPJ,UAOI,4BAPJ,CAOI,6BAPJ,oCAOI,oEAPJ,2BAOI,mCAPJ,CAOI,sBAPJ,wCAOI,6BAPJ,sCAOI,qCAPJ,4BAOI,CAPJ,yBAOI,UAPJ,8BAOI,CAPJ,2BAOI,UAPJ,4BAOI,CAPJ,yBAOI,4lBAPJ,4BAOI,sCAPJ,sCAOI,qCAPJ,UAOI,oTAPJ,4BAOI,kDAPJ,4BAOI,aAPJ,4BAOI,UAPJ,uBAOI,+NAPJ,CAOI,mCAPJ,aAOI,0BAPJ,UAOI,kCAPJ,uBAOI,kGAPJ,UAOI,kCAPJ,sBAOI,yCAPJ,8BAOI,2GAPJ,CAOI,4BAPJ,wCAOI,qEAPJ,CAOI,4BAPJ,WAOI,iNAPJ,8BAOI,uCAPJ,6BAOI,sCAPJ,+BAOI,wCAPJ,6BAOI,sCAPJ,+BAOI,wCAPJ,6BAOI,kEAPJ,WAOI,kHAPJ,0BAOI,uCAPJ,WAOI,sMAPJ,4BAOI,8KAPJ,wCAOI,+BAPJ,CAOI,mNAPJ,2BAOI,WAPJ,6BAOI,WAPJ,2BAOI,WAPJ,6BAOI,WAPJ,2BAOI,SAPJ,mBAOI,iEAPJ,SAOI,sBAPJ,SAOI,iCAPJ,sBAOI,oKAPJ,8BAOI,yGAPJ,CAOI,4BAPJ,uCAOI,+BAPJ,qCAOI,qEAPJ,8BAOI,uGAPJ,uBAOI,UAPJ,sEAOI,8BAPJ,CAOI,2BAPJ,UAOI,6BAPJ,CAOI,0BAPJ,yCAOI,6BAPJ,UAOI,8BAPJ,0BAOI,0CAPJ,4BAOI,mGAPJ,CAOI,0EAPJ,UAOI,gEAPJ,UAOI,gEAPJ,UAOI,6DAPJ,wCAOI,uCAPJ,UAOI,oEAPJ,UAOI,oEAPJ,UAOI,4BAPJ,UAOI,0BAPJ,UAOI,+B1CVR,wC0CUQ,gRAPJ,CAOI,0EAPJ,UAOI,kEAPJ,uCAOI,qCAPJ,WAOI,0BAPJ,oBAOI,8BAPJ,WAOI,kBAPJ,WAOI,+BAPJ,kBAOI,+BAPJ,CAOI,4BAPJ,eAOI,kCAPJ,wBAOI,eAPJ,uBAOI,eAPJ,sBAOI,sDAPJ,sBAOI,yHAPJ,uBAOI,gGAPJ,0DAOI,kBAPJ,wDAOI,kBAPJ,gCAOI,8CAPJ,8BAOI,0BAPJ,kDAOI,4BAPJ,kBAOI,8BAPJ,0BAOI,yCAPJ,cAOI,2CAPJ,2BAOI,0CAPJ,oBAOI,qCAPJ,mCAOI,wBAPJ,+BAOI,6BAPJ,sBAOI,6BAPJ,2BAOI,qBAPJ,4BAOI,0BAPJ,sBAOI,kCAPJ,gCAOI,qBAPJ,4BAOI,0BAPJ,sCAOI,uFAPJ,iCAOI,oFAPJ,iBAOI,2BAPJ,kBAOI,4BAPJ,kCAOI,mBAPJ,6BAOI,kCAPJ,eAOI,uBAPJ,2DAOI,0KAPJ,sCAOI,yGAPJ,eAOI,qJAPJ,oCAOI,yBAPJ,kCAOI,4BAPJ,gCAOI,kTAPJ,wBAOI,6GAPJ,6BAOI,yBAPJ,kCAOI,6OAPJ,2BAOI,mLAPJ,uBAOI,yGAPJ,4BAOI,gDAPJ,iBAOI,0FAPJ,aAOI,kBAPJ,YAOI,iCAPJ,iBAOI,0FAPJ,SAOI,8BAPJ,uBAOI,4FAPJ,YAOI,qBAPJ,UAOI,yJAPJ,CAOI,4BAPJ,oCAOI,mEAPJ,6BAOI,oCAPJ,CAOI,2BAPJ,UAOI,4BAPJ,CAOI,6BAPJ,oCAOI,oEAPJ,2BAOI,mCAPJ,CAOI,sBAPJ,wCAOI,6BAPJ,sCAOI,qCAPJ,4BAOI,CAPJ,yBAOI,UAPJ,8BAOI,CAPJ,2BAOI,UAPJ,4BAOI,CAPJ,yBAOI,4lBAPJ,4BAOI,sCAPJ,sCAOI,qCAPJ,UAOI,oTAPJ,4BAOI,kDAPJ,4BAOI,aAPJ,4BAOI,UAPJ,uBAOI,+NAPJ,CAOI,mCAPJ,aAOI,0BAPJ,UAOI,kCAPJ,uBAOI,kGAPJ,UAOI,kCAPJ,sBAOI,yCAPJ,8BAOI,2GAPJ,CAOI,4BAPJ,wCAOI,qEAPJ,CAOI,4BAPJ,WAOI,iNAPJ,8BAOI,uCAPJ,6BAOI,sCAPJ,+BAOI,wCAPJ,6BAOI,sCAPJ,+BAOI,wCAPJ,6BAOI,kEAPJ,WAOI,kHAPJ,0BAOI,uCAPJ,WAOI,sMAPJ,4BAOI,8KAPJ,wCAOI,+BAPJ,CAOI,mNAPJ,2BAOI,WAPJ,6BAOI,WAPJ,2BAOI,WAPJ,6BAOI,WAPJ,2BAOI,SAPJ,mBAOI,iEAPJ,SAOI,sBAPJ,SAOI,iCAPJ,sBAOI,oKAPJ,8BAOI,yGAPJ,CAOI,4BAPJ,uCAOI,+BAPJ,qCAOI,qEAPJ,8BAOI,uGAPJ,uBAOI,UAPJ,sEAOI,8BAPJ,CAOI,2BAPJ,UAOI,6BAPJ,CAOI,0BAPJ,yCAOI,6BAPJ,UAOI,8BAPJ,0BAOI,0CAPJ,4BAOI,mGAPJ,CAOI,0EAPJ,UAOI,gEAPJ,UAOI,gEAPJ,UAOI,6DAPJ,wCAOI,uCAPJ,UAOI,oEAPJ,UAOI,oEAPJ,UAOI,4BAPJ,UAOI,0BAPJ,UAOI,+B1CVR,wC0CUQ,gRAPJ,UAOI,iEAPJ,UAOI,kEAPJ,uCAOI,qCAPJ,WAOI,0BAPJ,oBAOI,8BAPJ,WAOI,kBAPJ,WAOI,+BAPJ,kBAOI,+BAPJ,WAOI,kBAPJ,eAOI,kCAPJ,wBAOI,eAPJ,uBAOI,eAPJ,sBAOI,qMAPJ,uBAOI,gGAPJ,0DAOI,kBAPJ,wDAOI,kBAPJ,gCAOI,sGAPJ,kDAOI,4BAPJ,kBAOI,8BAPJ,0BAOI,yCAPJ,cAOI,2CAPJ,2BAOI,2CAPJ,oBAOI,qCAPJ,mCAOI,wBAPJ,+BAOI,6BAPJ,sBAOI,6BAPJ,2BAOI,qBAPJ,4BAOI,0BAPJ,sBAOI,kCAPJ,gCAOI,qBAPJ,4BAOI,0BAPJ,sCAOI,uFAPJ,iCAOI,oFAPJ,iBAOI,2BAPJ,kBAOI,4BAPJ,kCAOI,mBAPJ,6BAOI,kCAPJ,eAOI,uBAPJ,2DAOI,0KAPJ,sCAOI,yGAPJ,eAOI,qJAPJ,oCAOI,yBAPJ,kCAOI,4BAPJ,gCAOI,kTAPJ,wBAOI,6GAPJ,6BAOI,yBAPJ,kCAOI,6OAPJ,2BAOI,mLAPJ,uBAOI,yGAPJ,4BAOI,gDAPJ,iBAOI,0FAPJ,aAOI,kBAPJ,YAOI,iCAPJ,iBAOI,0FAPJ,SAOI,8BAPJ,uBAOI,4FAPJ,YAOI,qBAPJ,UAOI,yJAPJ,CAOI,4BAPJ,oCAOI,mEAPJ,6BAOI,oCAPJ,CAOI,2BAPJ,UAOI,4BAPJ,CAOI,6BAPJ,oCAOI,oEAPJ,2BAOI,mCAPJ,CAOI,sBAPJ,wCAOI,6BAPJ,sCAOI,qCAPJ,4BAOI,CAPJ,yBAOI,UAPJ,8BAOI,CAPJ,2BAOI,UAPJ,4BAOI,CAPJ,yBAOI,4lBAPJ,4BAOI,sCAPJ,sCAOI,qCAPJ,UAOI,oTAPJ,4BAOI,kDAPJ,4BAOI,aAPJ,4BAOI,UAPJ,uBAOI,+NAPJ,CAOI,mCAPJ,aAOI,0BAPJ,UAOI,kCAPJ,uBAOI,kGAPJ,UAOI,kCAPJ,sBAOI,yCAPJ,8BAOI,2GAPJ,CAOI,4BAPJ,wCAOI,qEAPJ,CAOI,4BAPJ,WAOI,iNAPJ,8BAOI,uCAPJ,6BAOI,sCAPJ,+BAOI,wCAPJ,6BAOI,sCAPJ,+BAOI,wCAPJ,6BAOI,kEAPJ,WAOI,kHAPJ,0BAOI,uCAPJ,WAOI,sMAPJ,4BAOI,8KAPJ,wCAOI,+BAPJ,CAOI,mNAPJ,2BAOI,WAPJ,6BAOI,WAPJ,2BAOI,WAPJ,6BAOI,WAPJ,2BAOI,SAPJ,mBAOI,iEAPJ,SAOI,sBAPJ,SAOI,iCAPJ,sBAOI,oKAPJ,8BAOI,yGAPJ,CAOI,4BAPJ,uCAOI,+BAPJ,qCAOI,qEAPJ,8BAOI,uGAPJ,uBAOI,UAPJ,sEAOI,8BAPJ,CAOI,2BAPJ,UAOI,6BAPJ,CAOI,0BAPJ,yCAOI,6BAPJ,UAOI,8BAPJ,0BAOI,0CAPJ,4BAOI,mGAPJ,CAOI,0EAPJ,UAOI,gEAPJ,UAOI,gEAPJ,UAOI,6DAPJ,wCAOI,uCAPJ,UAOI,oEAPJ,UAOI,oEAPJ,UAOI,4BAPJ,UAOI,0BAPJ,UAOI,+B1CVR,wC0CUQ,uMAPJ,UAOI,+DAPJ,UAOI,iEAPJ,UAOI,mEAPJ,sCAOI,sCAPJ,yBAOI,+BAPJ,WAOI,8BAPJ,kBAOI,WAPJ,oBAOI,6BAPJ,WAOI,+BAPJ,kBAOI,eAPJ,mBAOI,6EAPJ,eAOI,sBAPJ,eAOI,wBAPJ,eAOI,sBAPJ,eAOI,wBAPJ,eAOI,wCAPJ,2BAOI,uBAPJ,kBAOI,gCAPJ,4BAOI,4EAPJ,kBAOI,8BAPJ,CAOI,yBAPJ,kBAOI,gCAPJ,4BAOI,kBAPJ,wDAOI,kDAPJ,4BAOI,kBAPJ,8BAOI,0BAPJ,yCAOI,wCAPJ,iBAOI,sDAPJ,iBAOI,oCAPJ,qBAOI,qCAPJ,yBAOI,+BAPJ,6BAOI,uBAPJ,6BAOI,2BAPJ,sBAOI,6BAPJ,yBAOI,yFAPJ,kDAOI,0BAPJ,eAOI,wBAPJ,mDAOI,cAPJ,uBAOI,oCAPJ,gDAOI,cAPJ,uBAOI,4FAPJ,aAOI,sBAPJ,oBAOI,6BAPJ,mCAOI,uCAPJ,eAOI,4BAPJ,kBAOI,6KAPJ,qBAOI,uCAPJ,oBAOI,2CAPJ,uBAOI,wCAPJ,kBAOI,kDAPJ,0HAOI,kCAPJ,6BAOI,yTAPJ,8BAOI,8GAPJ,0BAOI,sOAPJ,qCAOI,yKAPJ,uBAOI,oNAPJ,4BAOI,kDAPJ,iBAOI,cAPJ,iBAOI,8DAPJ,cAOI,kBAPJ,aAOI,kCAPJ,iBAOI,6FAPJ,UAOI,qBAPJ,UAOI,iCAPJ,qBAOI,kGAPJ,kCAOI,oIAPJ,CAOI,4BAPJ,qCAOI,oEAPJ,6BAOI,qCAPJ,CAOI,2BAPJ,wCAOI,6BAPJ,qCAOI,qEAPJ,2BAOI,oCAPJ,CAOI,sBAPJ,yCAOI,qEAPJ,0BAOI,kbAPJ,WAOI,gEAPJ,WAOI,iVAPJ,2BAOI,wCAPJ,WAOI,mEAPJ,WAOI,oMAPJ,4BAOI,WAPJ,8BAOI,WAPJ,4BAOI,WAPJ,8BAOI,WAPJ,4BAOI,cAPJ,4BAOI,WAPJ,uBAOI,WAPJ,4BAOI,WAPJ,2BAOI,wQAPJ,WAOI,kCAPJ,sBAOI,uGAPJ,WAOI,kCAPJ,6BAOI,CAPJ,8BAOI,yCAPJ,6BAOI,uCAPJ,CAOI,4BAPJ,YAOI,4DAPJ,uCAOI,6BAPJ,YAOI,6BAPJ,CAOI,8BAPJ,YAOI,2BAPJ,CAOI,4BAPJ,YAOI,4KAPJ,0BAOI,4CAPJ,4BAOI,0CAPJ,0BAOI,4CAPJ,4BAOI,0CAPJ,0BAOI,YAPJ,4BAOI,uCAPJ,YAOI,oHAPJ,4BAOI,sCAPJ,YAOI,2HAPJ,8BAOI,yCAPJ,WAOI,oMAPJ,YAOI,iNAPJ,4BAOI,YAPJ,2BAOI,YAPJ,6BAOI,YAPJ,2BAOI,YAPJ,6BAOI,YAPJ,2BAOI,UAPJ,mBAOI,mGAPJ,UAOI,yBAPJ,+BAOI,UAPJ,wBAOI,8FAPJ,UAOI,uKAPJ,CAOI,4BAPJ,WAOI,4DAPJ,sCAOI,sEAPJ,8BAOI,kNAPJ,8BAOI,CAPJ,2BAOI,WAPJ,6BAOI,CAPJ,oEAOI,CAPJ,4BAOI,yCAPJ,0BAOI,0CAPJ,CAOI,uCAPJ,8BAOI,0BAPJ,kCAOI,uCAPJ,WAOI,gEAPJ,uCAOI,qCAPJ,uCAOI,qCAPJ,oCAOI,yCAPJ,wCAOI,uCAPJ,yCAOI,uCAPJ,WAOI,qEAPJ,WAOI,0BAPJ,WAOI,+BAPJ,WAOI,8BCnCZ,WD4BQ,6BAOI,WAPJ,+BAOI,6JAPJ,WAOI,6BAPJ,WAOI,4BAPJ,sCAOI,WAPJ,6BAOI,WAPJ,2BAOI,8EEvEV,YAGF,eACE,YACA,oBAIA,YACA,mBAEA,8BACA,YACA,oBChBF,YACE,kBCAA,yIAWA,qHAXA,uIAME,2BAKF,0EAEE,4BAbF,8HAWA,yGAXA,8HAWA,uGAXA,gIAWA,4GAXA,+HAWA,2GAXA,oHAME,eAKF,6EAEE,CAFF,UAEE,eAbF,0GAWA,YALE,UAKF,C7BiBA,wuDAEA,yBACA,mBACA,kD6BhCA,6HAWA,2FAEE,CtDuDO,uDyB/CT,6BACA,6BACA,uCACA,CAKA,4OAEA,wBACA,uC6BhCA,sHAME,qCAKF,2F7BQA,wDAEA,6BACA,CACA,4BACA,uCACA,CAIA,oOAEA,UACA,oD6BhCA,sHAWA,2FAEE,CtDuDO,oCyBhDT,wDAEA,6BACA,6BACA,uCACA,CAIA,4NAEA,UACA,kC6BhCA,8HAME,oCAKF,wFDCA,C5BOA,uDAEA,6BACA,yBACA,mCACA,CAKA,oOAEA,yCACA,oB6BhCA,2JAWA,2FDCA,C5BOA,uDAEA,6BACA,6BACA,uCACA,CAIA,oNAEA,UACA,kCACA,kD6BhCA,8HAWA,mJ7BSA,6BACA,6BACA,uCAEA,CAIA,4OAEA,uBACA,mBACA,kD6BhCA,2HAME,2BAKF,wH7BQA,6BAEA,6BACA,uCACA,CAIA,4NAEA,UACA,mCACA,qBACA,uD6BhCA,8HAWA,wH7BQA,6BAEA,6BACA,uCACA,CAKA,oPAEA,0BACA,mBACA,kD6BhCA,4HAME,2BAKF,wHAXA,kGAWA,kTCZF,8BAEE,oDAIA,oCAIA,qCAEA,2BAIA,2BAGF,qClDLE,wDkDKF,6BAMI,6BAKF,wCAIA,4NChCA,UACA,gBACA,mBAGE,oBAGF,8BAKE,0BAIA,0BAGF,oCCrBA,qCAGA,2BjD2DE,2BiD/DJ,qCjD+DI,wDkD/DJ,6BACE,6B1DyJiB,C0DtJnB,sCACE,CAOF,oPAWA,WAoBA,waAOI,aAGF,kDAKF,CALE,kBAKF,cACE,uBACA,CACA,YACA,aAGF,CAJE,iBACA,gBAGF,gBACE,eACA,0CClEA,WACA,C3DKa,c2DLb,iCAKA,yCAMA,WCbF,kGpD4EI,gCoD5EJ,+FCIE,iCAGA,mBACA,CAEA,iBAFA,iBACA,CACA,gDAGF,gBACE,WAGF,gBACE,WACA,gBACA,eAGF,cACE,UAMA,cAJA,MAIA,iBANA,4BAEA,CAIA,gGAQA,sGCrCA,+BCGA,uBACA,0DAHA,gCAEA,CACA,4DAEA,uCAEA,oCACA,yBCTA,YACA,8CxD0EE,oCwDvEA,gBAIJ,2DAME,gBAGF,QACE,mBAGF,eACE,wCAKA,oBAEA,gBACA,sGAOE,eACA,8CAKJ,kBAEE,uBAIA,gDAFA,gCAEA,uBAGA,aACE,iBAGF,CANA,aAEA,kBAIA,4DAEE,WAGF,CALA,UAKA,kDAQA,gBANE,aACA,CAHF,oBAQA,kBACE,eACA,0CACA,WACA,gDAQA,mBAJA,iBAEA,CAGA,UACA,CANA,eAIA,aACA,CAFA,WAGA,2DAOF,CAVE,UAUF,YACE,iBAIJ,oCACE,CADF,qBACE,kBACE,cxDtBA,4BwD2BF,iHAeA,mFAKE,0BAIJ,oBAEE,OCxHF,mBCTA,2BAEE,CDQA,wBCVF,CFiIE,YACA,CCzHF,sBD0HE,6BC1HF,gBD6HA,kHC7HA,CDyHE,UEhIA,wBACA,yGAEA,mBAGF,CAJE,gBAIF,aACE,sFAEA,aACA,cACA,CAJA,iBACA,CADA,gBACA,iBAGA,0CAIA,aACA,YACA,CACA,mBAIF,qBAEE,oBACA,CARA,UAQA,qBAGA,cADA,8BACA,CAGA,eACA,CANA,mBACA,CACA,2DAIA,iCACA,sBAEA,mEAEA,gFAOF,YAJI,UAIJ,sBAIA,kBACE,ClE8MsB,iBkE5MtB,CAFA,wCACA,CASF,WARE,cAQF,kBlEsMuC,WkE5MrC,8BAEE,6BAIJ,CAJI,MAIJ,4BANE,UAMF,4BAGE,2HACA,eAEA,wLAOF,cACE,2BlEsKwB,YkEpKxB,gBACA,iBACA,CAEA,+BACE,iBACA,CAJF,UAIE,eAGF,aACE,iBAKJ,WACE,MAEA,mBACA,CAHA,iBAGA,CAIA,sEAMA,sBAPF,CAUI,UlEwIqB,akEvHvB,CAdE,eACE,CAJF,eAsBJ,2BALE,kBlErDS,CkEwCL,kHCjHN,CDmIA,iBlEgCmB,YkEnBjB,CACA,qBCjJF,CDgJE,YChJF,cAGA,4BAFA,eAEA,CAFA,cAGA,8BCHC,oDACC,CCWE,0BDZH,CCSG,cACA,CAXJ,aACE,CACA,eAEA,CAHA,wBACA,CAIA,iBACA,qBAGE,CANF,qCDFA,uEAIA,0BAIF,CAJE,mBAIF,qBACC,SEXD,qBAQA,kBACC,8BAIC,mEtENa,2BuENb,6ECCA,2FCIA,0BACG,CDNH,aCMG,qKAgBH,aACA,gBAEA,aACA,CADA,cADA,iBACA,CAFA,eACA,CACA,sBACA,sBAEA,aACA,CAFA,oBAEA,sBAIF,0BAIA,cACE,CACA,aADA,WACA,CADA,UACA,oBAEA,iEAGA,SACA,kBAEA,oBAIF,+BACE,iBAGF,iBACE,sBAGF,CAKA,+BALA,6BAKA,gBACE,iBAGF,oBACE,UAGF,sBAEE,kBACA,CAFA,mBACA,CACA,aACA,CAHA,eADF,cAIE,oCAEA,qCACA,4BAIF,oCAEE,kBACA,CAHF,kBACE,kBACA,CAEA,YACA,CAHA,YACA,sBACA,CAFA,UAGA,WAIA,gBAGF,CAJA,uBACE,CADF,sBAIA,CAKA,mKACE,qBAGF,8EACE,aACA,oBAEA,mBAIF,CAPE,oBAOF,yFACE,yBACA,YACA,oBAGF,iBAEE,yEASF,wBAEI,sBCpIJ,iBACE,gBAGA,WACA,CADA,gBADA,iBACA,CAFA,iBACA,CAEA,wBACA,wBAIA,CAJA,kBAOA,sCAIF,CAKE,UACA,CAFA,YACA,CAZA,8BAEA,kBACA,CAIF,eACE,CACA,iBACA,CAFA,kBAKA,qBAGA,aACA,eACA,wBAIF,CAJE,kBAKA,sBAEA,UACA,CADA,eACA,YAEA,SACA,CALA,gBACA,CAHF,WACE,kBACA,CAGA,UAEA,2BAGA,QACA,kCAGF,uBACE,iBACA,CADA,UACA,oCACA,WACA,oCAGA,QACA,mCACA,qBACA,CAGF,8CAEE,sCAKF,YAPA,gCAOA,qDAMA,+BAEE,CAFF,cAIE,qBAGF,CALE,oBAEA,CAGF,oEAEE,eACA,CACA,qEAMA,cACA,CAHA,eAGA,wFAOF,kBACE,8FAOA,iBAEA,CAEA,UACA,CAFA,WACA,CAFA,mBACA,CACA,UACA,4FAYF,WAHE,cAGF,6CACE,yDAEA,+BAGF,2CAQE,wBAGF,uBACE,CAXA,qBAGF,eACE,2BAGA,CARF,iBAYE,oBAGA,cAEA,CAEA,mBAFA,iBAEA,CALA,eACA,cAIA,iBAGF,kBAQE,qBAGF,CAXA,eAGE,CAGF,oBAEE,CAGF,4CALA,SAHE,0BAHF,kBAMA,QAHE,KAoBA,CAZF,kBAUA,gCAEE,CAXA,4BAIF,CALA,2BACE,CAIF,qBAOE,4BAeF,wBAXA,+BACE,CADF,kBAGE,aACA,CACA,YAEA,eAIF,CALE,cACA,CANA,iBAEA,CAQF,qBANE,UAMF,4FASE,aACA,SADA,OAEA,oDAKF,yBACE,wBAEA,eACA,gBACA,mBACA,CADA,UACA,yCCjNA,4BAEA,CAcQ,aAHR,iBACA,cAEQ,CAXR,UAEQ,CALR,WACA,CAQA,QACA,CALQ,cACR,CALA,aACA,CAIA,eAEA,UACA,CACA,mBACA,CATA,iBACA,CAFA,WAOA,UAMQ,iCAEJ,kBAGI,+DAEV,CAFU,4BAEV,gGAFU,iBAUR,CARF,iBAMA,SAFE,OAEF,CAHE,SAKA,wDAIQ,0DAEV,yBACE,sDAIF,kBACE,sBAGF,sBACE,CAHA,iBAEF,QACE,CACA,4BAEF,yHAEU,+CAEV,4EAEU,iFAEV,yDAGE,YADA,MACA,mEAGF,8BACE,4DAIA,UAEF,CAFE,OAEF,4BAHE,UACA,SAEF,8EAKE,OAFF,UAEE,0BAEA,cAGA,YACA,CAHA,UAMF,oCAPE,cACA,kBAEA,CACA,WAGF,kDAKE,CALF,8EAKE,qBAOF,cACE,CANF,uBAaA,eAIA,CAXE,QACA,CAEF,iBACE,CAOF,uGAIE,CARF,sBAPE,cANF,aAOE,cAEF,kBATA,UAKA,UAME,iBAEF,CAbA,iBAEE,CAWF,0BAXE,iBAGF,CAQA,eAQE,qDAEF,6CAEE,0BAEF,oBACE,gDAEF,oDAGA,CACE,2BAEF,aACE,kBACA,oCAMA,iBAEF,sDAEE,aACA,CADA,WACA,oHAMG,yBAGH,oHAQA,2CAGQ,wFAMR,eAEA,CACA,2BAEF,CAHE,4BAGF,uGAEE,4BAEF,CAJA,WAIA,mEAEE,sDAEF,mCAME,CANF,cAME,QAUF,WAhBA,sCAgBA,4JAgBA,kGAIA,4EAEE,2BAEF,sGAKA,+GAIE,wBAGA,8EAKA,gDAGA,wBAEF,uCAGA,6EAGE,qBAHF,iBAGE,mBAEF,YACE,oCAGA,sBACA,CAWF,MACE,CARA,cAOF,gBALU,iBAEV,CAJE,iBAQA,oHAVA,mBAEA,CAJA,oBAEA,CAEA,YAIF,wBACE,uCAiBA,CAdA,gFAWF,eACE,YACA,CAFF,iBACE,CAHA,oBAEF,CACE,MACA,SACA,uHAOA,aACA,oFAEA,iBAEF,2HAQE,iIAQA,QACA,4FAMA,aACA,qGAKF,YACE,yFAOF,YAFE,UAEF,mGAKE,YACA,CAHF,mBAGE,kBAEF,2EACE,4CAEF,6CACE,iHAMA,uBACA,CAFQ,QAGR,uBAOA,mCAGA,sBACA,CALA,cACA,CAHA,UACA,gBACA,WAHA,mBACA,CAHA,iBACA,SACA,UAQA,6BACA,yBACA,8BAEF,yBACE,6BAEF,wFAEE,CAFF,KAEE,qCAKF,yCAEE,CALA,iCACA,CAEF,mCAEE,wCAEA,OACA,uCAEA,iCAEA,mCAEA,uCAEA,QACA,2BAEA,WACA,CAFA,aAEA,gCACA,mBACA,wBACA,0BACA,6BAEF,iDAKE,qBALF,+BAIA,WACE,CADF,WAJA,oBAIA,iCAJA,iBAIA,CACE,iBAEF,yBAHA,SAGA,yCAGE,aAEF,qBACE,CALA,mCACA,CAIA,0BACA,+CAMA,0BAKA,2CAUQ,oBAEV,CAVA,SAIE,YAMF,8DAIE,kCACA,CACA,+DAOA,+BAIA,yCA2BA,4BAMQ,0BAIA,sBAlCR,sBACA,CAoBA,wBAEQ,CAtBR,qBAQQ,0BACR,CAEF,oBAGA,CAEE,mBACA,CAHF,iBAEE,CACA,eACA,CACA,WACA,CAFA,mBACA,CARF,SAFE,kBAEF,CAWU,sBAWA,+CAGR,SAEF,2GASU,sBACR,CAHA,oBAEQ,CAHR,cACA,CAIA,mBACA,0DAKA,mBACA,CAWF,qDAXE,sBAEA,YACA,gBACA,sBAIQ,CAGV,8BAHU,4BACR,CAEF,oBAFE,iBAEF,oRAYE,YACA,gEAIF,0BAGA,yFAIE,4BAEF,6jBAkBE,SAEQ,eAIV,uDAGE,kCAEF,8BALA,wCAKA,UAPE,UADA,SACA,gBAEF,CAKA,wBALA,eAKA,6BAGE,2BAEF,oIAMA,qBANA,sBAIU,CAJV,gBAIU,WAEV,kDAFU,eAEV,oCANA,iBAMA,mYAmBE,uCACA,CAHA,cACA,UAEA,CAEF,gGAGE,kBAEF,CAFE,qBAEF,ilBAmCE,kBAIA,CAGA,oBAEF,CALE,eAEQ,WAGV,4GAQE,2BAGA,sGAUF,2BAEE,CACA,2MAcF,4BACE,uIAQF,kBACE,wBAEQ,eAER,4CAGA,2OAeF,gDAFE,uBAEF,eAEE,2EAMF,wEAGE,eACA,4CAGA,uBACA,iBACG,2BAEK,cAGA,wBAEV,mDAIE,0BAGF,CAHE,cAGF,4KAyBY,sBAGR,CAEQ,YARV,uBAGU,CAGR,cAEQ,CAvBV,aAcF,eACE,CADF,UASY,2BCrxBV,qBACA,CAFF,0BACE,CACA,eACA,uBAGE,qBAGF,CAJA,8BAIA,iBAMA,qBACE,CALA,aACA,CAKA,YACA,CANA,QAGF,kCAJE,UAKA,eACA,CARF,iBASE,uBAGA,qEAGE,4FAGE,kFAGA,wBAEA,6CAGF,oDACE,gCAUF,sD5EkHa,C4E9GX,oBACA,CAZF,+BACE,iBADF,gBAOA,2HALE,iBAUA,sCAKN,8FAKE,eAIA,2HAWA,iBACA,CARA,4EAEE,wBAIJ,uCACE,CAPA,QAQA,kCAGA,2MAIA,kFAEE,YACA,iCAIJ,wFAGE,iB5EkEe,U4EhEf,gGAEE,cCpGN,CDmGM,cACA,CAFF,UClGJ,2CAIA,mBACE,CACA,YACA,gBACA,CAHA,iBAGA,iDAIA,gE7E6Nc", "sources": ["webpack://@adminkit/core/./src/scss/app.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_root.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/vendor/_rfs.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_reboot.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_variables.scss", "webpack://@adminkit/core/./src/scss/1-variables/_app.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_border-radius.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_type.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_lists.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_images.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_image.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_containers.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_container.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_breakpoints.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_grid.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_grid.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_tables.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_table-variants.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/forms/_labels.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/forms/_form-text.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/forms/_form-control.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_transition.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_gradients.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/forms/_form-select.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/forms/_form-check.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/forms/_form-range.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/forms/_floating-labels.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/forms/_input-group.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_forms.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_buttons.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_buttons.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_transitions.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_dropdown.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_caret.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_button-group.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_nav.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_navbar.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_card.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_badge.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_list-group.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/_close.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_clearfix.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_color-bg.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_colored-links.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_focus-ring.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_icon-link.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_ratio.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_position.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_stacks.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_visually-hidden.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_visually-hidden.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_stretched-link.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_text-truncation.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_text-truncate.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/helpers/_vr.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/mixins/_utilities.scss", "webpack://@adminkit/core/./node_modules/bootstrap/scss/utilities/_api.scss", "webpack://@adminkit/core/./src/scss/3-components/_avatar.scss", "webpack://@adminkit/core/./src/scss/3-components/_buttons.scss", "webpack://@adminkit/core/./src/scss/2-mixins/_button.scss", "webpack://@adminkit/core/./src/scss/3-components/_card.scss", "webpack://@adminkit/core/./src/scss/3-components/_chart.scss", "webpack://@adminkit/core/./src/scss/3-components/_content.scss", "webpack://@adminkit/core/./src/scss/3-components/_dropdown.scss", "webpack://@adminkit/core/./src/scss/3-components/_feather.scss", "webpack://@adminkit/core/./src/scss/3-components/_footer.scss", "webpack://@adminkit/core/./src/scss/3-components/_hamburger.scss", "webpack://@adminkit/core/./src/scss/3-components/_list-group.scss", "webpack://@adminkit/core/./src/scss/3-components/_main.scss", "webpack://@adminkit/core/./src/scss/3-components/_navbar.scss", "webpack://@adminkit/core/./src/scss/3-components/_reboot.scss", "webpack://@adminkit/core/./src/scss/3-components/_sidebar.scss", "webpack://@adminkit/core/./src/scss/3-components/_sizing.scss", "webpack://@adminkit/core/./src/scss/3-components/_tables.scss", "webpack://@adminkit/core/./src/scss/3-components/_stat.scss", "webpack://@adminkit/core/./src/scss/3-components/_type.scss", "webpack://@adminkit/core/./src/scss/3-components/_wrapper.scss", "webpack://@adminkit/core/./src/scss/4-utilities/_cursors.scss", "webpack://@adminkit/core/./node_modules/jsvectormap/dist/css/jsvectormap.css", "webpack://@adminkit/core/./node_modules/simplebar/dist/simplebar.css", "webpack://@adminkit/core/./node_modules/flatpickr/dist/flatpickr.css", "webpack://@adminkit/core/./src/scss/5-vendor/_flatpickr.scss", "webpack://@adminkit/core/./src/scss/5-vendor/_simplebar.scss"], "sourcesContent": ["/*!\r\n * AdminKit v3.4.0 (https://adminkit.io/)\r\n * Copyright 2023 <PERSON>\r\n * Copyright 2023 AdminKit\r\n * Licensed under MIT (https://github.com/adminkit/adminkit/blob/master/LICENSE)\r\n */\r\n\r\n// Variables\r\n@import \"1-variables/app\";\r\n\r\n// Bootstrap\r\n@import \"~bootstrap/scss/functions\";\r\n@import \"~bootstrap/scss/variables\";\r\n@import \"~bootstrap/scss/maps\";\r\n@import \"~bootstrap/scss/mixins\";\r\n@import \"~bootstrap/scss/utilities\";\r\n@import \"~bootstrap/scss/root\";\r\n@import \"~bootstrap/scss/reboot\";\r\n@import \"~bootstrap/scss/type\";\r\n@import \"~bootstrap/scss/images\";\r\n@import \"~bootstrap/scss/containers\";\r\n@import \"~bootstrap/scss/grid\";\r\n@import \"~bootstrap/scss/tables\";\r\n@import \"~bootstrap/scss/forms\";\r\n@import \"~bootstrap/scss/buttons\";\r\n@import \"~bootstrap/scss/transitions\";\r\n@import \"~bootstrap/scss/dropdown\";\r\n@import \"~bootstrap/scss/button-group\";\r\n@import \"~bootstrap/scss/nav\";\r\n@import \"~bootstrap/scss/navbar\";\r\n@import \"~bootstrap/scss/card\";\r\n@import \"~bootstrap/scss/badge\";\r\n@import \"~bootstrap/scss/list-group\";\r\n@import \"~bootstrap/scss/close\";\r\n@import \"~bootstrap/scss/helpers\";\r\n@import \"~bootstrap/scss/utilities/api\";\r\n\r\n// Theme mixins\r\n@import \"2-mixins/button\";\r\n\r\n// Theme components\r\n@import \"3-components/avatar\";\r\n@import \"3-components/buttons\";\r\n@import \"3-components/card\";\r\n@import \"3-components/chart\";\r\n@import \"3-components/content\";\r\n@import \"3-components/dropdown\";\r\n@import \"3-components/feather\";\r\n@import \"3-components/footer\";\r\n@import \"3-components/hamburger\";\r\n@import \"3-components/list-group\";\r\n@import \"3-components/main\";\r\n@import \"3-components/navbar\";\r\n@import \"3-components/reboot\";\r\n@import \"3-components/sidebar\";\r\n@import \"3-components/sizing\";\r\n@import \"3-components/stat\";\r\n@import \"3-components/tables\";\r\n@import \"3-components/type\";\r\n@import \"3-components/wrapper\";\r\n\r\n// Theme utilities\r\n@import \"4-utilities/cursors\";\r\n\r\n// 3rd party plugins\r\n@import \"~jsvectormap/dist/css/jsvectormap\";\r\n@import \"~simplebar/dist/simplebar\";\r\n@import \"~flatpickr/dist/flatpickr\";\r\n\r\n// 3rd party plugin styles\r\n@import \"5-vendor/flatpickr\";\r\n@import \"5-vendor/simplebar\";\r\n", ":root,\n[data-bs-theme=\"light\"] {\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color, $value in $colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $grays {\n    --#{$prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-rgb {\n    --#{$prefix}#{$color}-rgb: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-text {\n    --#{$prefix}#{$color}-text-emphasis: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-bg-subtle {\n    --#{$prefix}#{$color}-bg-subtle: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-border-subtle {\n    --#{$prefix}#{$color}-border-subtle: #{$value};\n  }\n\n  --#{$prefix}white-rgb: #{to-rgb($white)};\n  --#{$prefix}black-rgb: #{to-rgb($black)};\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$prefix}gradient: #{$gradient};\n\n  // Root and body\n  // scss-docs-start root-body-variables\n  @if $font-size-root != null {\n    --#{$prefix}root-font-size: #{$font-size-root};\n  }\n  --#{$prefix}body-font-family: #{inspect($font-family-base)};\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\n  --#{$prefix}body-font-weight: #{$font-weight-base};\n  --#{$prefix}body-line-height: #{$line-height-base};\n  @if $body-text-align != null {\n    --#{$prefix}body-text-align: #{$body-text-align};\n  }\n\n  --#{$prefix}body-color: #{$body-color};\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$prefix}body-bg: #{$body-bg};\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  --#{$prefix}emphasis-color: #{$body-emphasis-color};\n  --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color)};\n\n  --#{$prefix}secondary-color: #{$body-secondary-color};\n  --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color)};\n  --#{$prefix}secondary-bg: #{$body-secondary-bg};\n  --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg)};\n\n  --#{$prefix}tertiary-color: #{$body-tertiary-color};\n  --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color)};\n  --#{$prefix}tertiary-bg: #{$body-tertiary-bg};\n  --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg)};\n  // scss-docs-end root-body-variables\n\n  --#{$prefix}heading-color: #{$headings-color};\n\n  --#{$prefix}link-color: #{$link-color};\n  --#{$prefix}link-color-rgb: #{to-rgb($link-color)};\n  --#{$prefix}link-decoration: #{$link-decoration};\n\n  --#{$prefix}link-hover-color: #{$link-hover-color};\n  --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color)};\n\n  @if $link-hover-decoration != null {\n    --#{$prefix}link-hover-decoration: #{$link-hover-decoration};\n  }\n\n  --#{$prefix}code-color: #{$code-color};\n  --#{$prefix}highlight-bg: #{$mark-bg};\n\n  // scss-docs-start root-border-var\n  --#{$prefix}border-width: #{$border-width};\n  --#{$prefix}border-style: #{$border-style};\n  --#{$prefix}border-color: #{$border-color};\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\n\n  --#{$prefix}border-radius: #{$border-radius};\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\n  --#{$prefix}border-radius-xxl: #{$border-radius-xxl};\n  --#{$prefix}border-radius-2xl: var(--#{$prefix}border-radius-xxl); // Deprecated in v5.3.0 for consistency\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\n  // scss-docs-end root-border-var\n\n  --#{$prefix}box-shadow: #{$box-shadow};\n  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};\n  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};\n  --#{$prefix}box-shadow-inset: #{$box-shadow-inset};\n\n  // Focus styles\n  // scss-docs-start root-focus-variables\n  --#{$prefix}focus-ring-width: #{$focus-ring-width};\n  --#{$prefix}focus-ring-opacity: #{$focus-ring-opacity};\n  --#{$prefix}focus-ring-color: #{$focus-ring-color};\n  // scss-docs-end root-focus-variables\n\n  // scss-docs-start root-form-validation-variables\n  --#{$prefix}form-valid-color: #{$form-valid-color};\n  --#{$prefix}form-valid-border-color: #{$form-valid-border-color};\n  --#{$prefix}form-invalid-color: #{$form-invalid-color};\n  --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color};\n  // scss-docs-end root-form-validation-variables\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark, true) {\n    color-scheme: dark;\n\n    // scss-docs-start root-dark-mode-vars\n    --#{$prefix}body-color: #{$body-color-dark};\n    --#{$prefix}body-color-rgb: #{to-rgb($body-color-dark)};\n    --#{$prefix}body-bg: #{$body-bg-dark};\n    --#{$prefix}body-bg-rgb: #{to-rgb($body-bg-dark)};\n\n    --#{$prefix}emphasis-color: #{$body-emphasis-color-dark};\n    --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color-dark)};\n\n    --#{$prefix}secondary-color: #{$body-secondary-color-dark};\n    --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color-dark)};\n    --#{$prefix}secondary-bg: #{$body-secondary-bg-dark};\n    --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg-dark)};\n\n    --#{$prefix}tertiary-color: #{$body-tertiary-color-dark};\n    --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color-dark)};\n    --#{$prefix}tertiary-bg: #{$body-tertiary-bg-dark};\n    --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg-dark)};\n\n    @each $color, $value in $theme-colors-text-dark {\n      --#{$prefix}#{$color}-text-emphasis: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-bg-subtle-dark {\n      --#{$prefix}#{$color}-bg-subtle: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-border-subtle-dark {\n      --#{$prefix}#{$color}-border-subtle: #{$value};\n    }\n\n    --#{$prefix}heading-color: #{$headings-color-dark};\n\n    --#{$prefix}link-color: #{$link-color-dark};\n    --#{$prefix}link-hover-color: #{$link-hover-color-dark};\n    --#{$prefix}link-color-rgb: #{to-rgb($link-color-dark)};\n    --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color-dark)};\n\n    --#{$prefix}code-color: #{$code-color-dark};\n\n    --#{$prefix}border-color: #{$border-color-dark};\n    --#{$prefix}border-color-translucent: #{$border-color-translucent-dark};\n\n    --#{$prefix}form-valid-color: #{$form-valid-color-dark};\n    --#{$prefix}form-valid-border-color: #{$form-valid-border-color-dark};\n    --#{$prefix}form-invalid-color: #{$form-invalid-color-dark};\n    --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color-dark};\n    // scss-docs-end root-dark-mode-vars\n  }\n}\n", "// stylelint-disable scss/dimension-no-non-numeric-values\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit != rem and $rfs-unit != px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit == px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n@else if $rfs-base-value-unit == rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode == max-media-query {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class == enable {\n    @if $rfs-mode == min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query () {\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n  @else {\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n    @include _rfs-media-query () {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    }\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      @if $unit == px {\n        // Convert to rem if needed\n        $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n      @else if $unit == rem {\n        // Convert to px if needed\n        $val: $val + \" \" + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      } @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + \" \" + $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    } @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit != px and $unit != rem {\n        $val: $val + \" \" + $value;\n      } @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\n          $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + \" calc(\" + $min-width + if($value < 0, \" - \", \" + \") + $variable-width + \")\";\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values != null {\n    $val: rfs-value($values);\n    $fluid-val: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val == $fluid-val {\n      #{$property}: $val;\n    }\n    @else {\n      @include _rfs-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluid-val);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $fluid-val, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n\n// Root\n//\n// Ability to the value of the root font sizes, affecting the value of `rem`.\n// null by default, thus nothing is generated.\n\n:root {\n  @if $font-size-root != null {\n    @include font-size(var(--#{$prefix}root-font-size));\n  }\n\n  @if $enable-smooth-scroll {\n    @media (prefers-reduced-motion: no-preference) {\n      scroll-behavior: smooth;\n    }\n  }\n}\n\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Prevent adjustments of font size after orientation changes in iOS.\n// 4. Change the default tap highlight to be completely transparent in iOS.\n\n// scss-docs-start reboot-body-rules\nbody {\n  margin: 0; // 1\n  font-family: var(--#{$prefix}body-font-family);\n  @include font-size(var(--#{$prefix}body-font-size));\n  font-weight: var(--#{$prefix}body-font-weight);\n  line-height: var(--#{$prefix}body-line-height);\n  color: var(--#{$prefix}body-color);\n  text-align: var(--#{$prefix}body-text-align);\n  background-color: var(--#{$prefix}body-bg); // 2\n  -webkit-text-size-adjust: 100%; // 3\n  -webkit-tap-highlight-color: rgba($black, 0); // 4\n}\n// scss-docs-end reboot-body-rules\n\n\n// Content grouping\n//\n// 1. Reset Firefox's gray color\n\nhr {\n  margin: $hr-margin-y 0;\n  color: $hr-color; // 1\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n  opacity: $hr-opacity;\n}\n\n\n// Typography\n//\n// 1. Remove top margins from headings\n//    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n//    margin for easier control within type scales as it avoids margin collapsing.\n\n%heading {\n  margin-top: 0; // 1\n  margin-bottom: $headings-margin-bottom;\n  font-family: $headings-font-family;\n  font-style: $headings-font-style;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: var(--#{$prefix}heading-color);\n}\n\nh1 {\n  @extend %heading;\n  @include font-size($h1-font-size);\n}\n\nh2 {\n  @extend %heading;\n  @include font-size($h2-font-size);\n}\n\nh3 {\n  @extend %heading;\n  @include font-size($h3-font-size);\n}\n\nh4 {\n  @extend %heading;\n  @include font-size($h4-font-size);\n}\n\nh5 {\n  @extend %heading;\n  @include font-size($h5-font-size);\n}\n\nh6 {\n  @extend %heading;\n  @include font-size($h6-font-size);\n}\n\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\n\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n\n// Abbreviations\n//\n// 1. Add the correct text decoration in Chrome, Edge, Opera, and Safari.\n// 2. Add explicit cursor to indicate changed behavior.\n// 3. Prevent the text-decoration to be skipped.\n\nabbr[title] {\n  text-decoration: underline dotted; // 1\n  cursor: help; // 2\n  text-decoration-skip-ink: none; // 3\n}\n\n\n// Address\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\n\n// Lists\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\n// 1. Undo browser default\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // 1\n}\n\n\n// Blockquote\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\n\n// Strong\n//\n// Add the correct font weight in Chrome, Edge, and Safari\n\nb,\nstrong {\n  font-weight: $font-weight-bolder;\n}\n\n\n// Small\n//\n// Add the correct font size in all browsers\n\nsmall {\n  @include font-size($small-font-size);\n}\n\n\n// Mark\n\nmark {\n  padding: $mark-padding;\n  background-color: var(--#{$prefix}highlight-bg);\n}\n\n\n// Sub and Sup\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n\nsub,\nsup {\n  position: relative;\n  @include font-size($sub-sup-font-size);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n// Links\n\na {\n  color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, 1));\n  text-decoration: $link-decoration;\n\n  &:hover {\n    --#{$prefix}link-color-rgb: var(--#{$prefix}link-hover-color-rgb);\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([class]) {\n  &,\n  &:hover {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n// Code\n\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-code;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n}\n\n// 1. Remove browser default top margin\n// 2. Reset browser default of `1em` to use `rem`s\n// 3. Don't allow content to break outside\n\npre {\n  display: block;\n  margin-top: 0; // 1\n  margin-bottom: 1rem; // 2\n  overflow: auto; // 3\n  @include font-size($code-font-size);\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\ncode {\n  @include font-size($code-font-size);\n  color: var(--#{$prefix}code-color);\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n\n  kbd {\n    padding: 0;\n    @include font-size(1em);\n    font-weight: $nested-kbd-font-weight;\n  }\n}\n\n\n// Figures\n//\n// Apply a consistent margin strategy (matches our type styles).\n\nfigure {\n  margin: 0 0 1rem;\n}\n\n\n// Images and content\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\n\n// Tables\n//\n// Prevent double borders\n\ntable {\n  caption-side: bottom;\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: $table-cell-padding-y;\n  padding-bottom: $table-cell-padding-y;\n  color: $table-caption-color;\n  text-align: left;\n}\n\n// 1. Removes font-weight bold by inheriting\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\n// 3. Fix alignment for Safari\n\nth {\n  font-weight: $table-th-font-weight; // 1\n  text-align: inherit; // 2\n  text-align: -webkit-match-parent; // 3\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\n\n// Forms\n//\n// 1. Allow labels to use `margin` for spacing.\n\nlabel {\n  display: inline-block; // 1\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n// See https://github.com/twbs/bootstrap/issues/24093\n\nbutton {\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 0;\n}\n\n// Explicitly remove focus outline in Chromium when it shouldn't be\n// visible (e.g. as result of mouse click or touch tap). It already\n// should be doing this automatically, but seems to currently be\n// confused and applies its very visible two-tone outline anyway.\n\nbutton:focus:not(:focus-visible) {\n  outline: 0;\n}\n\n// 1. Remove the margin in Firefox and Safari\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // 1\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n}\n\n// Remove the inheritance of text transform in Firefox\nbutton,\nselect {\n  text-transform: none;\n}\n// Set the cursor for non-`<button>` buttons\n//\n// Details at https://github.com/twbs/bootstrap/pull/30562\n[role=\"button\"] {\n  cursor: pointer;\n}\n\nselect {\n  // Remove the inheritance of word-wrap in Safari.\n  // See https://github.com/twbs/bootstrap/issues/24990\n  word-wrap: normal;\n\n  // Undo the opacity change from Chrome\n  &:disabled {\n    opacity: 1;\n  }\n}\n\n// Remove the dropdown arrow only from text type inputs built with datalists in Chrome.\n// See https://stackoverflow.com/a/54997118\n\n[list]:not([type=\"date\"]):not([type=\"datetime-local\"]):not([type=\"month\"]):not([type=\"week\"]):not([type=\"time\"])::-webkit-calendar-picker-indicator {\n  display: none !important;\n}\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\n// 3. Opinionated: add \"hand\" cursor to non-disabled button elements.\n\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n\n  @if $enable-button-pointers {\n    &:not(:disabled) {\n      cursor: pointer; // 3\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\n// 1. Textareas should really only resize vertically so they don't break their (horizontal) containers.\n\ntextarea {\n  resize: vertical; // 1\n}\n\n// 1. Browsers set a default `min-width: min-content;` on fieldsets,\n//    unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n//    So we reset that to ensure fieldsets behave more like a standard block element.\n//    See https://github.com/twbs/bootstrap/issues/12359\n//    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n// 2. Reset the default outline behavior of fieldsets so they don't affect page layout.\n\nfieldset {\n  min-width: 0; // 1\n  padding: 0; // 2\n  margin: 0; // 2\n  border: 0; // 2\n}\n\n// 1. By using `float: left`, the legend will behave like a block element.\n//    This way the border of a fieldset wraps around the legend if present.\n// 2. Fix wrapping bug.\n//    See https://github.com/twbs/bootstrap/issues/29712\n\nlegend {\n  float: left; // 1\n  width: 100%;\n  padding: 0;\n  margin-bottom: $legend-margin-bottom;\n  @include font-size($legend-font-size);\n  font-weight: $legend-font-weight;\n  line-height: inherit;\n\n  + * {\n    clear: left; // 2\n  }\n}\n\n// Fix height of inputs with a type of datetime-local, date, month, week, or time\n// See https://github.com/twbs/bootstrap/issues/18842\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n// 1. Correct the outline style in Safari.\n// 2. This overrides the extra rounded corners on search inputs in iOS so that our\n//    `.form-control` class can properly style them. Note that this cannot simply\n//    be added to `.form-control` as it's not specific enough. For details, see\n//    https://github.com/twbs/bootstrap/issues/11586.\n\n[type=\"search\"] {\n  outline-offset: -2px; // 1\n  -webkit-appearance: textfield; // 2\n}\n\n// 1. A few input types should stay LTR\n// See https://rtlstyling.com/posts/rtl-styling#form-inputs\n// 2. RTL only output\n// See https://rtlcss.com/learn/usage-guide/control-directives/#raw\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n\n// Remove the inner padding in Chrome and Safari on macOS.\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n// Remove padding around color pickers in webkit browsers\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n\n// 1. Inherit font family and line height for file input buttons\n// 2. Correct the inability to style clickable types in iOS and Safari.\n\n::file-selector-button {\n  font: inherit; // 1\n  -webkit-appearance: button; // 2\n}\n\n// Correct element displays\n\noutput {\n  display: inline-block;\n}\n\n// Remove border from iframe\n\niframe {\n  border: 0;\n}\n\n// Summary\n//\n// 1. Add the correct display in all browsers\n\nsummary {\n  display: list-item; // 1\n  cursor: pointer;\n}\n\n\n// Progress\n//\n// Add the correct vertical alignment in Chrome, Firefox, and Opera.\n\nprogress {\n  vertical-align: baseline;\n}\n\n\n// Hidden attribute\n//\n// Always hide an element with the `hidden` HTML attribute.\n\n[hidden] {\n  display: none !important;\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"black\":      $black,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-text-variables\n$primary-text-emphasis:   shade-color($primary, 60%) !default;\n$secondary-text-emphasis: shade-color($secondary, 60%) !default;\n$success-text-emphasis:   shade-color($success, 60%) !default;\n$info-text-emphasis:      shade-color($info, 60%) !default;\n$warning-text-emphasis:   shade-color($warning, 60%) !default;\n$danger-text-emphasis:    shade-color($danger, 60%) !default;\n$light-text-emphasis:     $gray-700 !default;\n$dark-text-emphasis:      $gray-700 !default;\n// scss-docs-end theme-text-variables\n\n// scss-docs-start theme-bg-subtle-variables\n$primary-bg-subtle:       tint-color($primary, 80%) !default;\n$secondary-bg-subtle:     tint-color($secondary, 80%) !default;\n$success-bg-subtle:       tint-color($success, 80%) !default;\n$info-bg-subtle:          tint-color($info, 80%) !default;\n$warning-bg-subtle:       tint-color($warning, 80%) !default;\n$danger-bg-subtle:        tint-color($danger, 80%) !default;\n$light-bg-subtle:         mix($gray-100, $white) !default;\n$dark-bg-subtle:          $gray-400 !default;\n// scss-docs-end theme-bg-subtle-variables\n\n// scss-docs-start theme-border-subtle-variables\n$primary-border-subtle:   tint-color($primary, 60%) !default;\n$secondary-border-subtle: tint-color($secondary, 60%) !default;\n$success-border-subtle:   tint-color($success, 60%) !default;\n$info-border-subtle:      tint-color($info, 60%) !default;\n$warning-border-subtle:   tint-color($warning, 60%) !default;\n$danger-border-subtle:    tint-color($danger, 60%) !default;\n$light-border-subtle:     $gray-200 !default;\n$dark-border-subtle:      $gray-500 !default;\n// scss-docs-end theme-border-subtle-variables\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-container-classes:    true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n$enable-dark-mode:            true !default;\n$color-mode-type:             data !default; // `data` or `media-query`\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix:                      $variable-prefix !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-text-align:           null !default;\n$body-color:                $gray-900 !default;\n$body-bg:                   $white !default;\n\n$body-secondary-color:      rgba($body-color, .75) !default;\n$body-secondary-bg:         $gray-200 !default;\n\n$body-tertiary-color:       rgba($body-color, .5) !default;\n$body-tertiary-bg:          $gray-100 !default;\n\n$body-emphasis-color:       $black !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        shift-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Icon links\n// scss-docs-start icon-link-variables\n$icon-link-gap:               .375rem !default;\n$icon-link-underline-offset:  .25em !default;\n$icon-link-icon-size:         1em !default;\n$icon-link-icon-transition:   .2s ease-in-out transform !default;\n$icon-link-icon-transform:    translate3d(.25em, 0, 0) !default;\n// scss-docs-end icon-link-variables\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px !default;\n$border-widths: (\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n$border-style:                solid !default;\n$border-color:                $gray-300 !default;\n$border-color-translucent:    rgba($black, .175) !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .375rem !default;\n$border-radius-sm:            .25rem !default;\n$border-radius-lg:            .5rem !default;\n$border-radius-xl:            1rem !default;\n$border-radius-xxl:           2rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n// fusv-disable\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\n// fusv-enable\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n// scss-docs-start focus-ring-variables\n$focus-ring-width:      .25rem !default;\n$focus-ring-opacity:    .25 !default;\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\n$focus-ring-blur:       0 !default;\n$focus-ring-box-shadow: 0 0 $focus-ring-blur $focus-ring-width $focus-ring-color !default;\n// scss-docs-end focus-ring-variables\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings, and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-medium:          500 !default;\n$font-weight-semibold:        600 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer * .5 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-family: null !default;\n$display-font-style:  null !default;\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n// fusv-disable\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\n// fusv-enable\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n\n// fusv-disable\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\n$hr-height:                   null !default; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color:             null !default; // Allows for inherited colors\n$hr-border-width:             var(--#{$prefix}border-width) !default;\n$hr-opacity:                  .25 !default;\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-padding:                .1875em !default;\n$mark-bg:                     $yellow-100 !default;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 var(--#{$prefix}body-color) !default;\n$table-bg:                    var(--#{$prefix}body-bg) !default;\n$table-accent-bg:             transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor:         .1 !default;\n$table-border-width:          var(--#{$prefix}border-width) !default;\n$table-border-color:          var(--#{$prefix}border-color) !default;\n\n$table-striped-order:         odd !default;\n$table-striped-columns-order: even !default;\n\n$table-group-separator-color: currentcolor !default;\n\n$table-caption-color:         var(--#{$prefix}secondary-color) !default;\n\n$table-bg-scale:              -80% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         $focus-ring-width !default;\n$input-btn-focus-color-opacity: $focus-ring-opacity !default;\n$input-btn-focus-color:         $focus-ring-color !default;\n$input-btn-focus-blur:          $focus-ring-blur !default;\n$input-btn-focus-box-shadow:    $focus-ring-box-shadow !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-color:                   var(--#{$prefix}body-color) !default;\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              var(--#{$prefix}link-color) !default;\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\n$btn-link-disabled-color:     $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           var(--#{$prefix}border-radius) !default;\n$btn-border-radius-sm:        var(--#{$prefix}border-radius-sm) !default;\n$btn-border-radius-lg:        var(--#{$prefix}border-radius-lg) !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$btn-hover-bg-shade-amount:       15% !default;\n$btn-hover-bg-tint-amount:        15% !default;\n$btn-hover-border-shade-amount:   20% !default;\n$btn-hover-border-tint-amount:    10% !default;\n$btn-active-bg-shade-amount:      20% !default;\n$btn-active-bg-tint-amount:       20% !default;\n$btn-active-border-shade-amount:  25% !default;\n$btn-active-border-tint-amount:   10% !default;\n// scss-docs-end btn-variables\n\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              var(--#{$prefix}body-bg) !default;\n$input-disabled-color:                  null !default;\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           var(--#{$prefix}body-color) !default;\n$input-border-color:                    var(--#{$prefix}border-color) !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      $box-shadow-inset !default;\n\n$input-border-radius:                   var(--#{$prefix}border-radius) !default;\n$input-border-radius-sm:                var(--#{$prefix}border-radius-sm) !default;\n$input-border-radius-lg:                var(--#{$prefix}border-radius-lg) !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\n$input-plaintext-color:                 var(--#{$prefix}body-color) !default;\n\n$input-height-border:                   calc(#{$input-border-width} * 2) !default; // stylelint-disable-line function-disallowed-list\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y * .5) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-color-width:                      3rem !default;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-check-inline-margin-end:    1rem !default;\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba($black, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-start:       $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n$form-switch-transition:          background-position .15s ease-in-out !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  var(--#{$prefix}tertiary-bg) !default;\n$input-group-addon-border-color:        $input-border-color !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        null !default;\n$form-select-disabled-bg:           $input-disabled-bg !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $input-border-radius !default;\n$form-select-box-shadow:          $box-shadow-inset !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             var(--#{$prefix}tertiary-bg) !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     $box-shadow-inset !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             var(--#{$prefix}secondary-color) !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             var(--#{$prefix}tertiary-bg) !default;\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:                  add(3.5rem, $input-height-border) !default;\n$form-floating-line-height:             1.25 !default;\n$form-floating-padding-x:               $input-padding-x !default;\n$form-floating-padding-y:               1rem !default;\n$form-floating-input-padding-t:         1.625rem !default;\n$form-floating-input-padding-b:         .625rem !default;\n$form-floating-label-height:            1.5em !default;\n$form-floating-label-opacity:           .65 !default;\n$form-floating-label-transform:         scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-label-disabled-color:    $gray-600 !default;\n$form-floating-transition:              opacity .1s ease-in-out, transform .1s ease-in-out !default;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-colors\n$form-valid-color:                  $form-feedback-valid-color !default;\n$form-valid-border-color:           $form-feedback-valid-color !default;\n$form-invalid-color:                $form-feedback-invalid-color !default;\n$form-invalid-border-color:         $form-feedback-invalid-color !default;\n// scss-docs-end form-validation-colors\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": var(--#{$prefix}form-valid-color),\n    \"icon\": $form-feedback-icon-valid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}success),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}success-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\n  ),\n  \"invalid\": (\n    \"color\": var(--#{$prefix}form-invalid-color),\n    \"icon\": $form-feedback-icon-invalid,\n    \"tooltip-color\": #fff,\n    \"tooltip-bg-color\": var(--#{$prefix}danger),\n    \"focus-box-shadow\": 0 0 $input-btn-focus-blur $input-focus-width rgba(var(--#{$prefix}danger-rgb), $input-btn-focus-color-opacity),\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n$zindex-toast:                      1090 !default;\n// scss-docs-end zindex-stack\n\n// scss-docs-start zindex-levels-map\n$zindex-levels: (\n  n1: -1,\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3\n) !default;\n// scss-docs-end zindex-levels-map\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    var(--#{$prefix}link-color) !default;\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           var(--#{$prefix}secondary-color) !default;\n$nav-link-focus-box-shadow:         $focus-ring-box-shadow !default;\n\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\n$nav-tabs-link-active-border-color: var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           var(--#{$prefix}border-radius) !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-underline-gap:                 1rem !default;\n$nav-underline-border-width:        .125rem !default;\n$nav-underline-link-active-color:   var(--#{$prefix}emphasis-color) !default;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer * .5 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\n$navbar-brand-margin-end:           1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n\n$navbar-light-color:                rgba(var(--#{$prefix}emphasis-color-rgb), .65) !default;\n$navbar-light-hover-color:          rgba(var(--#{$prefix}emphasis-color-rgb), .8) !default;\n$navbar-light-active-color:         rgba(var(--#{$prefix}emphasis-color-rgb), 1) !default;\n$navbar-light-disabled-color:       rgba(var(--#{$prefix}emphasis-color-rgb), .3) !default;\n$navbar-light-icon-color:           rgba($body-color, .75) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-icon-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba(var(--#{$prefix}emphasis-color-rgb), .15) !default;\n$navbar-light-brand-color:          $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:    $navbar-light-active-color !default;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-dark-variables\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n$navbar-dark-brand-color:           $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\n// scss-docs-end navbar-dark-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\n$dropdown-border-color:             var(--#{$prefix}border-color-translucent) !default;\n$dropdown-border-radius:            var(--#{$prefix}border-radius) !default;\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\n$dropdown-inner-border-radius:      calc(#{$dropdown-border-radius} - #{$dropdown-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .5 !default;\n$dropdown-box-shadow:               $box-shadow !default;\n\n$dropdown-link-color:               var(--#{$prefix}body-color) !default;\n$dropdown-link-hover-color:         $dropdown-link-color !default;\n$dropdown-link-hover-bg:            var(--#{$prefix}tertiary-bg) !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      var(--#{$prefix}tertiary-color) !default;\n\n$dropdown-item-padding-y:           $spacer * .25 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\n$dropdown-header-padding-y:         $dropdown-padding-y !default;\n// fusv-disable\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n// scss-docs-end dropdown-dark-variables\n\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-font-size:              $font-size-base !default;\n\n$pagination-color:                  var(--#{$prefix}link-color) !default;\n$pagination-bg:                     var(--#{$prefix}body-bg) !default;\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\n$pagination-margin-start:           calc(#{$pagination-border-width} * -1) !default; // stylelint-disable-line function-disallowed-list\n$pagination-border-color:           var(--#{$prefix}border-color) !default;\n\n$pagination-focus-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-focus-bg:               var(--#{$prefix}secondary-bg) !default;\n$pagination-focus-box-shadow:       $focus-ring-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            var(--#{$prefix}link-hover-color) !default;\n$pagination-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\n$pagination-hover-border-color:     var(--#{$prefix}border-color) !default; // Todo in v6: remove this?\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $component-active-bg !default;\n\n$pagination-disabled-color:         var(--#{$prefix}secondary-color) !default;\n$pagination-disabled-bg:            var(--#{$prefix}secondary-bg) !default;\n$pagination-disabled-border-color:  var(--#{$prefix}border-color) !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$pagination-border-radius-sm:       var(--#{$prefix}border-radius-sm) !default;\n$pagination-border-radius-lg:       var(--#{$prefix}border-radius-lg) !default;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-title-color:                  null !default;\n$card-subtitle-color:               null !default;\n$card-border-width:                 var(--#{$prefix}border-width) !default;\n$card-border-color:                 var(--#{$prefix}border-color-translucent) !default;\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y * .5 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba(var(--#{$prefix}body-color-rgb), .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           var(--#{$prefix}body-bg) !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         var(--#{$prefix}body-color) !default;\n$accordion-bg:                            var(--#{$prefix}body-bg) !default;\n$accordion-border-width:                  var(--#{$prefix}border-width) !default;\n$accordion-border-color:                  var(--#{$prefix}border-color) !default;\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width) !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  var(--#{$prefix}body-color) !default;\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              var(--#{$prefix}primary-bg-subtle) !default;\n$accordion-button-active-color:           var(--#{$prefix}primary-text-emphasis) !default;\n\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $body-color !default;\n$accordion-icon-active-color:             $primary-text-emphasis !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(-180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     var(--#{$prefix}body-bg) !default;\n$tooltip-bg:                        var(--#{$prefix}emphasis-color) !default;\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer * .25 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    null !default; // TODO: remove this in v6\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n// fusv-disable\n$tooltip-arrow-color:               null !default; // Deprecated in Bootstrap 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              var(--#{$prefix}border-width) !default;\n$popover-border-color:              var(--#{$prefix}border-color-translucent) !default;\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\n$popover-inner-border-radius:       calc(#{$popover-border-radius} - #{$popover-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$popover-box-shadow:                $box-shadow !default;\n\n$popover-header-font-size:          $font-size-base !default;\n$popover-header-bg:                 var(--#{$prefix}secondary-bg) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                var(--#{$prefix}body-color) !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color:               $popover-bg !default;\n$popover-arrow-outer-color:         var(--#{$prefix}border-color-translucent) !default;\n// fusv-enable\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-border-width:                var(--#{$prefix}border-width) !default;\n$toast-border-color:                var(--#{$prefix}border-color-translucent) !default;\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                var(--#{$prefix}secondary-color) !default;\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-header-border-color:         $toast-border-color !default;\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               var(--#{$prefix}border-radius) !default;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer !default;\n\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\n$modal-content-border-color:        var(--#{$prefix}border-color-translucent) !default;\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n\n$modal-header-border-color:         var(--#{$prefix}border-color) !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-footer-bg:                   null !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:               $spacer !default;\n$alert-padding-x:               $spacer !default;\n$alert-margin-bottom:           1rem !default;\n$alert-border-radius:           var(--#{$prefix}border-radius) !default;\n$alert-link-font-weight:        $font-weight-bold !default;\n$alert-border-width:            var(--#{$prefix}border-width) !default;\n$alert-bg-scale:                -80% !default;\n$alert-border-scale:            -70% !default;\n$alert-color-scale:             40% !default;\n$alert-dismissible-padding-r:   $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// fusv-disable\n$alert-bg-scale:                -80% !default; // Deprecated in v5.2.0, to be removed in v6\n$alert-border-scale:            -70% !default; // Deprecated in v5.2.0, to be removed in v6\n$alert-color-scale:             40% !default; // Deprecated in v5.2.0, to be removed in v6\n// fusv-enable\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       var(--#{$prefix}secondary-bg) !default;\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  var(--#{$prefix}body-color) !default;\n$list-group-bg:                     var(--#{$prefix}body-bg) !default;\n$list-group-border-color:           var(--#{$prefix}border-color) !default;\n$list-group-border-width:           var(--#{$prefix}border-width) !default;\n$list-group-border-radius:          var(--#{$prefix}border-radius) !default;\n\n$list-group-item-padding-y:         $spacer * .5 !default;\n$list-group-item-padding-x:         $spacer !default;\n// fusv-disable\n$list-group-item-bg-scale:          -80% !default; // Deprecated in v5.3.0\n$list-group-item-color-scale:       40% !default; // Deprecated in v5.3.0\n// fusv-enable\n\n$list-group-hover-bg:               var(--#{$prefix}tertiary-bg) !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         var(--#{$prefix}secondary-color) !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           var(--#{$prefix}secondary-color) !default;\n$list-group-action-hover-color:     var(--#{$prefix}emphasis-color) !default;\n\n$list-group-action-active-color:    var(--#{$prefix}body-color) !default;\n$list-group-action-active-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\n$thumbnail-border-color:            var(--#{$prefix}border-color) !default;\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              0 !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          var(--#{$prefix}secondary-color) !default;\n$breadcrumb-active-color:           var(--#{$prefix}secondary-color) !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n// scss-docs-end carousel-variables\n\n// scss-docs-start carousel-dark-variables\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n// scss-docs-end carousel-dark-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $focus-ring-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n// scss-docs-end close-variables\n\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                var(--#{$prefix}body-bg) !default;\n$offcanvas-color:                   var(--#{$prefix}body-color) !default;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs !default;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .1875rem !default;\n$kbd-padding-x:                     .375rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         var(--#{$prefix}body-bg) !default;\n$kbd-bg:                            var(--#{$prefix}body-color) !default;\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\n\n$pre-color:                         null !default;\n", "// Fonts\r\n$font-family-sans-serif: \"<PERSON>\", \"Helvetica Neue\", Arial, -apple-system, BlinkMacSystemFont, \"Segoe UI\", <PERSON><PERSON>, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\r\n$font-weight-light: 300 !default;\r\n$font-weight-normal: 400 !default;\r\n$font-weight-bold: 600 !default;\r\n$font-size-base: .875rem !default;\r\n$font-size-lg: .925rem !default;\r\n$font-size-sm: .75rem !default;\r\n$small-font-size: 80% !default;\r\n\r\n// Spacer\r\n$spacer: 1rem !default;\r\n\r\n$spacers: (\r\n  0: 0,\r\n  1: ($spacer * .25),\r\n  2: ($spacer * .5),\r\n  3: $spacer,\r\n  4: ($spacer * 1.5),\r\n  5: ($spacer * 3),\r\n  6: ($spacer * 4.5),\r\n  7: ($spacer * 6)\r\n) !default;\r\n\r\n// Grid breakpoints\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px,\r\n  xxl: 1440px\r\n) !default;\r\n\r\n// Grid gutter\r\n$grid-gutter-width: 24px;\r\n\r\n// Grid containers\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1200px\r\n) !default;\r\n\r\n$container-padding-x: .75rem !default;\r\n\r\n// Options\r\n$min-contrast-ratio: 3 !default;\r\n$enable-validation-icons: false !default;\r\n$enable-rfs: false !default;\r\n$enable-negative-margins: true !default;\r\n$enable-dark-mode: false !default;\r\n\r\n// Transitions\r\n$transition-base: all .35s ease-in-out !default;\r\n\r\n// Theme colors\r\n$blue:    #3B7DDD !default;\r\n$indigo:  #0a0a0a !default;\r\n$purple:  #6f42c1 !default;\r\n$pink:    #e83e8c !default;\r\n$red:     #dc3545 !default;\r\n$orange:  #fd7e14 !default;\r\n$yellow:  #fcb92c !default;\r\n$green:   #1cbb8c !default;\r\n$teal:    #20c997 !default;\r\n$cyan:    #17a2b8 !default;\r\n\r\n$white:    #fff !default;\r\n$gray-100: #f8f9fa !default;\r\n$gray-200: #e9ecef !default;\r\n$gray-300: #dee2e6 !default;\r\n$gray-400: #ced4da !default;\r\n$gray-500: #adb5bd !default;\r\n$gray-600: #6c757d !default;\r\n$gray-700: #495057 !default;\r\n$gray-800: #343a40 !default;\r\n$gray-900: #212529 !default;\r\n$black: #000 !default;\r\n\r\n$primary:       $blue !default;\r\n$secondary:     $gray-600 !default;\r\n$success:       $green !default;\r\n$info:          $cyan !default;\r\n$warning:       $yellow !default;\r\n$danger:        $red !default;\r\n$light:         #f5f7fb !default;\r\n$dark:          $gray-900 !default;\r\n\r\n$theme-colors: (\r\n  \"primary\": $primary,\r\n  \"secondary\": $secondary,\r\n  \"success\": $success,\r\n  \"info\": $info,\r\n  \"warning\": $warning,\r\n  \"danger\": $danger,\r\n  \"light\": $light,\r\n  \"dark\": $dark\r\n) !default;\r\n\r\n// Social colors\r\n$social-colors: (\r\n  \"facebook\": #3b5998,\r\n  \"twitter\": #1da1f2,\r\n  \"google\": #dc4e41,\r\n  \"youtube\": #f00,\r\n  \"vimeo\": #1ab7ea,\r\n  \"dribbble\": #ea4c89,\r\n  \"github\": #181717,\r\n  \"instagram\": #e4405f,\r\n  \"pinterest\": #bd081c,\r\n  \"flickr\": #0063dc,\r\n  \"bitbucket\": #0052cc\r\n) !default;\r\n\r\n// Body\r\n$body-bg: #f5f7fb !default;\r\n$body-color: $gray-700 !default;\r\n\r\n// Links\r\n$link-decoration: none !default;\r\n$link-hover-decoration: underline !default;\r\n\r\n// Common\r\n$border-width: 1px !default;\r\n\r\n// Fonts\r\n$line-height-base: 1.5 !default;\r\n$line-height-lg: 1.5 !default;\r\n$line-height-sm: 1.5 !default;\r\n\r\n// Headings\r\n$h1-font-size: $font-size-base * 2 !default;\r\n$h2-font-size: $font-size-base * 1.75 !default;\r\n$h3-font-size: $font-size-base * 1.5 !default;\r\n$h4-font-size: $font-size-base * 1.25 !default;\r\n$h5-font-size: $font-size-base !default;\r\n$h6-font-size: $font-size-base !default;\r\n$headings-color: $black !default;\r\n$headings-font-weight: 400 !default;\r\n\r\n// Displays\r\n$display-font-sizes: (\r\n\t1: 6rem,\r\n  2: 5.5rem,\r\n  3: 4.5rem,\r\n  4: 3.5rem,\r\n  5: 3rem,\r\n  6: 2.5rem\r\n  ) !default;\r\n\r\n// Shadows\r\n$box-shadow-sm:    0 0.05rem 0.2rem rgba($black, .05) !default;\r\n$box-shadow:       0 0.1rem 0.2rem rgba($black, .05) !default;\r\n$box-shadow-lg:    0 0.2rem 0.2rem rgba($black, .05) !default;\r\n\r\n// Navbar input\r\n$navbar-input-bg: darken($body-bg, 0.5%) !default;\r\n\r\n// Border radius\r\n$border-radius-sm: .1rem !default;\r\n$border-radius:    .2rem !default;\r\n$border-radius-lg: .3rem !default;\r\n\r\n// Navbar\r\n$navbar-padding-y: $spacer*0.875 !default;\r\n$navbar-padding-x: $spacer*1.375 !default;\r\n$navbar-nav-link-padding-x: .5rem !default;\r\n$navbar-border-bottom: 0 !default;\r\n$navbar-box-shadow: 0 0 2rem 0 rgba($dark, .1) !default;\r\n\r\n// Navbar brand\r\n$navbar-brand-padding-y: $spacer*0.875 !default;\r\n$navbar-brand-padding-x: 0 !default;\r\n$navbar-brand-color: $gray-100 !default;\r\n$navbar-brand-font-weight: $font-weight-normal !default;\r\n$navbar-brand-font-size: 1.15rem !default;\r\n$navbar-brand-icon-color: $primary !default;\r\n\r\n$navbar-bg: $white !default;\r\n\r\n// Footer\r\n$footer-bg: $white !default;\r\n\r\n// Forms\r\n$input-bg: $white !default;\r\n$input-disabled-bg: $gray-200 !default;\r\n$input-color: $gray-700 !default;\r\n$input-btn-border-width: $border-width !default;\r\n$input-btn-line-height: $line-height-base !default;\r\n$input-btn-line-height-sm: $line-height-sm !default;\r\n$input-btn-line-height-lg: $line-height-lg !default;\r\n$input-btn-focus-width: .2rem !default;\r\n$input-btn-padding-y: .3rem !default;\r\n$input-btn-padding-y-sm: .2rem !default;\r\n$input-btn-padding-y-lg: .4rem !default;\r\n$input-btn-padding-x: .85rem !default;\r\n$input-border-width: $input-btn-border-width !default;\r\n$input-height-border: $input-border-width * 2 !default;\r\n$input-height-inner: ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\r\n$input-height: calc(#{$input-height-inner} + #{$input-height-border}) !default;\r\n$input-height-inner-sm: ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\r\n$input-height-sm: calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\r\n$input-height-inner-lg: ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\r\n$input-height-lg: calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\r\n\r\n// Cards\r\n$card-bg: $white !default;\r\n$card-border-radius: .25rem !default;\r\n$card-inner-border-radius: $card-border-radius !default;\r\n$card-border-width: 0 !default;\r\n$card-border-color: transparent !default;\r\n$card-cap-bg: $white !default;\r\n$card-shadow: 0 0 .875rem 0 rgba($dark, 0.05) !default;\r\n$card-title-font-size: $font-size-lg !default;\r\n$card-title-font-weight: $font-weight-bold !default;\r\n$card-title-color: lighten($gray-600, 15%) !default;\r\n$card-spacer-y: 1.25rem !default;\r\n$card-spacer-x: 1.25rem !default;\r\n$card-cap-padding-y: 1rem !default;\r\n$card-cap-padding-x: $card-spacer-x !default;\r\n\r\n// Tables\r\n$table-cell-padding-y: .75rem !default;\r\n$table-cell-padding-x: .75rem !default;\r\n$table-cell-padding-y-sm: .3rem !default;\r\n$table-cell-padding-x-sm: .3rem !default;\r\n$table-bg: transparent !default;\r\n$table-striped-bg: $gray-100 !default;\r\n$table-hover-bg: rgba($black, .0375) !default;\r\n\r\n// Sidebar general\r\n$sidebar-width: 260px !default;\r\n$sidebar-transition: margin-left .35s ease-in-out, left .35s ease-in-out, margin-right .35s ease-in-out, right .35s ease-in-out !default;\r\n$sidebar-bg: #222E3C !default;\r\n\r\n// Sidebar brand\r\n$sidebar-brand-padding-y: 1.15rem !default;\r\n$sidebar-brand-padding-x: 1.5rem !default;\r\n$sidebar-brand-font-weight: $font-weight-bold !default;\r\n$sidebar-brand-font-size: 1.15rem !default;\r\n$sidebar-brand-color: $gray-100 !default;\r\n\r\n// Sidebar header\r\n$sidebar-header-padding: 1.5rem 1.5rem 0.375rem !default;\r\n$sidebar-header-font-size: $font-size-sm !default;\r\n$sidebar-header-color: $gray-400 !default;\r\n\r\n// Sidebar link\r\n$sidebar-link-padding: .625rem 1.625rem !default;\r\n$sidebar-link-font-weight: 400 !default;\r\n$sidebar-link-bg: #222E3C !default;\r\n$sidebar-link-color: rgba($gray-200, 0.5) !default;\r\n$sidebar-link-border-left-color: transparent !default;\r\n\r\n$sidebar-link-hover-font-weight: $font-weight-normal !default;\r\n$sidebar-link-hover-bg: #222E3C !default;\r\n$sidebar-link-hover-color: #{rgba($gray-200, 0.75)} !default;\r\n$sidebar-link-hover-border-left-color: transparent !default;\r\n\r\n$sidebar-link-active-font-weight: $font-weight-normal !default;\r\n$sidebar-link-active-bg: #{linear-gradient(90deg, rgba($primary,0.1) 0%, rgba($primary,0.0875) 50%, rgba($black, 0) 100%)} !default;\r\n$sidebar-link-active-color: $gray-200 !default;\r\n$sidebar-link-active-border-left-color: $primary !default;\r\n\r\n// Sidebar link icon\r\n$sidebar-link-icon-color: #{rgba($gray-200, 0.5)} !default;\r\n$sidebar-link-icon-hover-color: #{rgba($gray-200, 0.75)} !default;\r\n$sidebar-link-icon-active-color: $gray-200 !default;\r\n\r\n// Sidebar bottom\r\n$sidebar-cta-color: $gray-200 !default;\r\n$sidebar-cta-bg: #2B3947 !default;\r\n$sidebar-cta-padding: $spacer*1.5 !default;\r\n$sidebar-cta-margin: $spacer*1.75 !default;\r\n$sidebar-cta-border-radius: $border-radius-lg !default;\r\n\r\n// Transitions\r\n$transition-appearance: background .4s ease-in-out, color .4s ease-in-out !default;\r\n$transition-appearance-slow: background .6s ease-in-out, color .6s ease-in-out !default;\r\n$transition-appearance-fast: background .1s ease-in-out, color .1s ease-in-out !default;\r\n\r\n// Modals\r\n$modal-content-border-width: 0 !default;\r\n$modal-header-border-width: 1px !default;\r\n$modal-footer-border-width: 1px !default;\r\n$modal-xl: 1140px !default;\r\n$modal-lg: 900px !default;\r\n$modal-md: 600px !default;\r\n$modal-sm: 400px !default;\r\n$modal-transition: transform .25s ease-out !default;\r\n\r\n// Badges\r\n$badge-font-size: 80% !default;\r\n$badge-padding-y: .3em !default;\r\n$badge-padding-x: .45em !default;\r\n\r\n// Dropdowns\r\n$dropdown-bg: $white !default;\r\n\r\n// Main\r\n$main-box-shadow: inset .75rem 0px 1.5rem 0px rgba($black, 0.075);\r\n\r\n// Content\r\n$content-padding-desktop: 3rem 3rem 1.5rem !default;\r\n$content-padding-mobile: 1.5rem 1.5rem .75rem !default;\r\n\r\n// Datetimepicker\r\n$bs-datetimepicker-active-bg: $primary !default;\r\n\r\n// Simplebar\r\n$simplebar-scrollbar-bg: $white !default;\r\n\r\n// Hamburger\r\n$hamburger-width-top: 24px !default;\r\n$hamburger-width-middle: 24px !default;\r\n$hamburger-width-bottom: 16px !default;\r\n\r\n// Stats\r\n$stat-bg: lighten($primary, 35%) !default;\r\n$stat-icon-color: $primary !default;\r\n$stat-padding: $spacer * 0.75 !default;", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n// scss-docs-start border-radius-mixins\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-end-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-start-radius($radius: $border-radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n// scss-docs-end border-radius-mixins\n", "//\n// Headings\n//\n.h1 {\n  @extend h1;\n}\n\n.h2 {\n  @extend h2;\n}\n\n.h3 {\n  @extend h3;\n}\n\n.h4 {\n  @extend h4;\n}\n\n.h5 {\n  @extend h5;\n}\n\n.h6 {\n  @extend h6;\n}\n\n\n.lead {\n  @include font-size($lead-font-size);\n  font-weight: $lead-font-weight;\n}\n\n// Type display classes\n@each $display, $font-size in $display-font-sizes {\n  .display-#{$display} {\n    @include font-size($font-size);\n    font-family: $display-font-family;\n    font-style: $display-font-style;\n    font-weight: $display-font-weight;\n    line-height: $display-line-height;\n  }\n}\n\n//\n// Emphasis\n//\n.small {\n  @extend small;\n}\n\n.mark {\n  @extend mark;\n}\n\n//\n// Lists\n//\n\n.list-unstyled {\n  @include list-unstyled();\n}\n\n// Inline turns list items into inline-block\n.list-inline {\n  @include list-unstyled();\n}\n.list-inline-item {\n  display: inline-block;\n\n  &:not(:last-child) {\n    margin-right: $list-inline-padding;\n  }\n}\n\n\n//\n// Misc\n//\n\n// Builds on `abbr`\n.initialism {\n  @include font-size($initialism-font-size);\n  text-transform: uppercase;\n}\n\n// Blockquotes\n.blockquote {\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-font-size);\n\n  > :last-child {\n    margin-bottom: 0;\n  }\n}\n\n.blockquote-footer {\n  margin-top: -$blockquote-margin-y;\n  margin-bottom: $blockquote-margin-y;\n  @include font-size($blockquote-footer-font-size);\n  color: $blockquote-footer-color;\n\n  &::before {\n    content: \"\\2014\\00A0\"; // em dash, nbsp\n  }\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled {\n  padding-left: 0;\n  list-style: none;\n}\n", "// Responsive images (ensure images don't scale beyond their parents)\n//\n// This is purposefully opt-in via an explicit class rather than being the default for all `<img>`s.\n// We previously tried the \"images are responsive by default\" approach in Bootstrap v2,\n// and abandoned it in Bootstrap v3 because it breaks lots of third-party widgets (including Google Maps)\n// which weren't expecting the images within themselves to be involuntarily resized.\n// See also https://github.com/twbs/bootstrap/issues/18178\n.img-fluid {\n  @include img-fluid();\n}\n\n\n// Image thumbnails\n.img-thumbnail {\n  padding: $thumbnail-padding;\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  @include border-radius($thumbnail-border-radius);\n  @include box-shadow($thumbnail-box-shadow);\n\n  // Keep them at most 100% wide\n  @include img-fluid();\n}\n\n//\n// Figures\n//\n\n.figure {\n  // Ensures the caption's text aligns with the image.\n  display: inline-block;\n}\n\n.figure-img {\n  margin-bottom: $spacer * .5;\n  line-height: 1;\n}\n\n.figure-caption {\n  @include font-size($figure-caption-font-size);\n  color: $figure-caption-color;\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-container-classes {\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint == $name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Container mixins\n\n@mixin make-container($gutter: $container-padding-x) {\n  --#{$prefix}gutter-x: #{$gutter};\n  --#{$prefix}gutter-y: 0;\n  width: 100%;\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-right: auto;\n  margin-left: auto;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Row\n//\n// Rows contain your columns.\n\n:root {\n  @each $name, $value in $grid-breakpoints {\n    --#{$prefix}breakpoint-#{$name}: #{$value};\n  }\n}\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    > * {\n      @include make-col-ready();\n    }\n  }\n}\n\n@if $enable-cssgrid {\n  .grid {\n    display: grid;\n    grid-template-rows: repeat(var(--#{$prefix}rows, 1), 1fr);\n    grid-template-columns: repeat(var(--#{$prefix}columns, #{$grid-columns}), 1fr);\n    gap: var(--#{$prefix}gap, #{$grid-gutter-width});\n\n    @include make-cssgrid();\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  --#{$prefix}gutter-x: #{$gutter};\n  --#{$prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  // TODO: Revisit calc order after https://github.com/react-bootstrap/react-bootstrap/issues/6039 is fixed\n  margin-top: calc(-1 * var(--#{$prefix}gutter-y)); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n}\n\n@mixin make-col-ready() {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$prefix}gutter-y);\n}\n\n@mixin make-col($size: false, $columns: $grid-columns) {\n  @if $size {\n    flex: 0 0 auto;\n    width: percentage(divide($size, $columns));\n\n  } @else {\n    flex: 1 1 0;\n    max-width: 100%;\n  }\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: divide($size, $columns);\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// number of columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: divide(100%, $count);\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n\n@mixin make-cssgrid($columns: $grid-columns, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .g-col#{$infix}-#{$i} {\n            grid-column: auto / span $i;\n          }\n        }\n\n        // Start with `1` because `0` is and invalid value.\n        // Ends with `$columns - 1` because offsetting by the width of an entire row isn't possible.\n        @for $i from 1 through ($columns - 1) {\n          .g-start#{$infix}-#{$i} {\n            grid-column-start: $i;\n          }\n        }\n      }\n    }\n  }\n}\n", "//\n// Basic Bootstrap table\n//\n\n.table {\n  // Reset needed for nesting tables\n  --#{$prefix}table-color-type: initial;\n  --#{$prefix}table-bg-type: initial;\n  --#{$prefix}table-color-state: initial;\n  --#{$prefix}table-bg-state: initial;\n  // End of reset\n  --#{$prefix}table-color: #{$table-color};\n  --#{$prefix}table-bg: #{$table-bg};\n  --#{$prefix}table-border-color: #{$table-border-color};\n  --#{$prefix}table-accent-bg: #{$table-accent-bg};\n  --#{$prefix}table-striped-color: #{$table-striped-color};\n  --#{$prefix}table-striped-bg: #{$table-striped-bg};\n  --#{$prefix}table-active-color: #{$table-active-color};\n  --#{$prefix}table-active-bg: #{$table-active-bg};\n  --#{$prefix}table-hover-color: #{$table-hover-color};\n  --#{$prefix}table-hover-bg: #{$table-hover-bg};\n\n  width: 100%;\n  margin-bottom: $spacer;\n  vertical-align: $table-cell-vertical-align;\n  border-color: var(--#{$prefix}table-border-color);\n\n  // Target th & td\n  // We need the child combinator to prevent styles leaking to nested tables which doesn't have a `.table` class.\n  // We use the universal selectors here to simplify the selector (else we would need 6 different selectors).\n  // Another advantage is that this generates less code and makes the selector less specific making it easier to override.\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    padding: $table-cell-padding-y $table-cell-padding-x;\n    // Following the precept of cascades: https://codepen.io/miriamsuzanne/full/vYNgodb\n    color: var(--#{$prefix}table-color-state, var(--#{$prefix}table-color-type, var(--#{$prefix}table-color)));\n    background-color: var(--#{$prefix}table-bg);\n    border-bottom-width: $table-border-width;\n    box-shadow: inset 0 0 0 9999px var(--#{$prefix}table-bg-state, var(--#{$prefix}table-bg-type, var(--#{$prefix}table-accent-bg)));\n  }\n\n  > tbody {\n    vertical-align: inherit;\n  }\n\n  > thead {\n    vertical-align: bottom;\n  }\n}\n\n.table-group-divider {\n  border-top: calc(#{$table-border-width} * 2) solid $table-group-separator-color; // stylelint-disable-line function-disallowed-list\n}\n\n//\n// Change placement of captions with a class\n//\n\n.caption-top {\n  caption-side: top;\n}\n\n\n//\n// Condensed table w/ half padding\n//\n\n.table-sm {\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    padding: $table-cell-padding-y-sm $table-cell-padding-x-sm;\n  }\n}\n\n\n// Border versions\n//\n// Add or remove borders all around the table and between all the columns.\n//\n// When borders are added on all sides of the cells, the corners can render odd when\n// these borders do not have the same color or if they are semi-transparent.\n// Therefor we add top and border bottoms to the `tr`s and left and right borders\n// to the `td`s or `th`s\n\n.table-bordered {\n  > :not(caption) > * {\n    border-width: $table-border-width 0;\n\n    // stylelint-disable-next-line selector-max-universal\n    > * {\n      border-width: 0 $table-border-width;\n    }\n  }\n}\n\n.table-borderless {\n  // stylelint-disable-next-line selector-max-universal\n  > :not(caption) > * > * {\n    border-bottom-width: 0;\n  }\n\n  > :not(:first-child) {\n    border-top-width: 0;\n  }\n}\n\n// Zebra-striping\n//\n// Default zebra-stripe styles (alternating gray and transparent backgrounds)\n\n// For rows\n.table-striped {\n  > tbody > tr:nth-of-type(#{$table-striped-order}) > * {\n    --#{$prefix}table-color-type: var(--#{$prefix}table-striped-color);\n    --#{$prefix}table-bg-type: var(--#{$prefix}table-striped-bg);\n  }\n}\n\n// For columns\n.table-striped-columns {\n  > :not(caption) > tr > :nth-child(#{$table-striped-columns-order}) {\n    --#{$prefix}table-color-type: var(--#{$prefix}table-striped-color);\n    --#{$prefix}table-bg-type: var(--#{$prefix}table-striped-bg);\n  }\n}\n\n// Active table\n//\n// The `.table-active` class can be added to highlight rows or cells\n\n.table-active {\n  --#{$prefix}table-color-state: var(--#{$prefix}table-active-color);\n  --#{$prefix}table-bg-state: var(--#{$prefix}table-active-bg);\n}\n\n// Hover effect\n//\n// Placed here since it has to come after the potential zebra striping\n\n.table-hover {\n  > tbody > tr:hover > * {\n    --#{$prefix}table-color-state: var(--#{$prefix}table-hover-color);\n    --#{$prefix}table-bg-state: var(--#{$prefix}table-hover-bg);\n  }\n}\n\n\n// Table variants\n//\n// Table variants set the table cell backgrounds, border colors\n// and the colors of the striped, hovered & active tables\n\n@each $color, $value in $table-variants {\n  @include table-variant($color, $value);\n}\n\n// Responsive tables\n//\n// Generate series of `.table-responsive-*` classes for configuring the screen\n// size of where your table will overflow.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n  @include media-breakpoint-down($breakpoint) {\n    .table-responsive#{$infix} {\n      overflow-x: auto;\n      -webkit-overflow-scrolling: touch;\n    }\n  }\n}\n", "// scss-docs-start table-variant\n@mixin table-variant($state, $background) {\n  .table-#{$state} {\n    $color: color-contrast(opaque($body-bg, $background));\n    $hover-bg: mix($color, $background, percentage($table-hover-bg-factor));\n    $striped-bg: mix($color, $background, percentage($table-striped-bg-factor));\n    $active-bg: mix($color, $background, percentage($table-active-bg-factor));\n    $table-border-color: mix($color, $background, percentage($table-border-factor));\n\n    --#{$prefix}table-color: #{$color};\n    --#{$prefix}table-bg: #{$background};\n    --#{$prefix}table-border-color: #{$table-border-color};\n    --#{$prefix}table-striped-bg: #{$striped-bg};\n    --#{$prefix}table-striped-color: #{color-contrast($striped-bg)};\n    --#{$prefix}table-active-bg: #{$active-bg};\n    --#{$prefix}table-active-color: #{color-contrast($active-bg)};\n    --#{$prefix}table-hover-bg: #{$hover-bg};\n    --#{$prefix}table-hover-color: #{color-contrast($hover-bg)};\n\n    color: var(--#{$prefix}table-color);\n    border-color: var(--#{$prefix}table-border-color);\n  }\n}\n// scss-docs-end table-variant\n", "//\n// Labels\n//\n\n.form-label {\n  margin-bottom: $form-label-margin-bottom;\n  @include font-size($form-label-font-size);\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  color: $form-label-color;\n}\n\n// For use with horizontal and inline forms, when you need the label (or legend)\n// text to align with the form controls.\n.col-form-label {\n  padding-top: add($input-padding-y, $input-border-width);\n  padding-bottom: add($input-padding-y, $input-border-width);\n  margin-bottom: 0; // Override the `<legend>` default\n  @include font-size(inherit); // Override the `<legend>` default\n  font-style: $form-label-font-style;\n  font-weight: $form-label-font-weight;\n  line-height: $input-line-height;\n  color: $form-label-color;\n}\n\n.col-form-label-lg {\n  padding-top: add($input-padding-y-lg, $input-border-width);\n  padding-bottom: add($input-padding-y-lg, $input-border-width);\n  @include font-size($input-font-size-lg);\n}\n\n.col-form-label-sm {\n  padding-top: add($input-padding-y-sm, $input-border-width);\n  padding-bottom: add($input-padding-y-sm, $input-border-width);\n  @include font-size($input-font-size-sm);\n}\n", "//\n// Form text\n//\n\n.form-text {\n  margin-top: $form-text-margin-top;\n  @include font-size($form-text-font-size);\n  font-style: $form-text-font-style;\n  font-weight: $form-text-font-weight;\n  color: $form-text-color;\n}\n", "//\n// General form controls (plus a few specific high-level interventions)\n//\n\n.form-control {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y $input-padding-x;\n  font-family: $input-font-family;\n  @include font-size($input-font-size);\n  font-weight: $input-font-weight;\n  line-height: $input-line-height;\n  color: $input-color;\n  background-color: $input-bg;\n  background-clip: padding-box;\n  border: $input-border-width solid $input-border-color;\n  appearance: none; // Fix appearance for date inputs in Safari\n\n  // Note: This has no effect on <select>s in some browsers, due to the limited stylability of `<select>`s in CSS.\n  @include border-radius($input-border-radius, 0);\n\n  @include box-shadow($input-box-shadow);\n  @include transition($input-transition);\n\n  &[type=\"file\"] {\n    overflow: hidden; // prevent pseudo element button overlap\n\n    &:not(:disabled):not([readonly]) {\n      cursor: pointer;\n    }\n  }\n\n  // Customize the `:focus` state to imitate native WebKit styles.\n  &:focus {\n    color: $input-focus-color;\n    background-color: $input-focus-bg;\n    border-color: $input-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($input-box-shadow, $input-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $input-focus-box-shadow;\n    }\n  }\n\n  &::-webkit-date-and-time-value {\n    // On Android Chrome, form-control's \"width: 100%\" makes the input width too small\n    // Tested under Android 11 / Chrome 89, Android 12 / Chrome 100, Android 13 / Chrome 109\n    //\n    // On iOS Safari, form-control's \"appearance: none\" + \"width: 100%\" makes the input width too small\n    // Tested under iOS 16.2 / Safari 16.2\n    min-width: 85px; // Seems to be a good minimum safe width\n\n    // Add some height to date inputs on iOS\n    // https://github.com/twbs/bootstrap/issues/23307\n    // TODO: we can remove this workaround once https://bugs.webkit.org/show_bug.cgi?id=198959 is resolved\n    // Multiply line-height by 1em if it has no unit\n    height: if(unit($input-line-height) == \"\", $input-line-height * 1em, $input-line-height);\n\n    // Android Chrome type=\"date\" is taller than the other inputs\n    // because of \"margin: 1px 24px 1px 4px\" inside the shadow DOM\n    // Tested under Android 11 / Chrome 89, Android 12 / Chrome 100, Android 13 / Chrome 109\n    margin: 0;\n  }\n\n  // Prevent excessive date input height in Webkit\n  // https://github.com/twbs/bootstrap/issues/34433\n  &::-webkit-datetime-edit {\n    display: block;\n    padding: 0;\n  }\n\n  // Placeholder\n  &::placeholder {\n    color: $input-placeholder-color;\n    // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526.\n    opacity: 1;\n  }\n\n  // Disabled inputs\n  //\n  // HTML5 says that controls under a fieldset > legend:first-child won't be\n  // disabled if the fieldset is disabled. Due to implementation difficulty, we\n  // don't honor that edge case; we style them as disabled anyway.\n  &:disabled {\n    color: $input-disabled-color;\n    background-color: $input-disabled-bg;\n    border-color: $input-disabled-border-color;\n    // iOS fix for unreadable disabled content; see https://github.com/twbs/bootstrap/issues/11655.\n    opacity: 1;\n  }\n\n  // File input buttons theming\n  &::file-selector-button {\n    padding: $input-padding-y $input-padding-x;\n    margin: (-$input-padding-y) (-$input-padding-x);\n    margin-inline-end: $input-padding-x;\n    color: $form-file-button-color;\n    @include gradient-bg($form-file-button-bg);\n    pointer-events: none;\n    border-color: inherit;\n    border-style: solid;\n    border-width: 0;\n    border-inline-end-width: $input-border-width;\n    border-radius: 0; // stylelint-disable-line property-disallowed-list\n    @include transition($btn-transition);\n  }\n\n  &:hover:not(:disabled):not([readonly])::file-selector-button {\n    background-color: $form-file-button-hover-bg;\n  }\n}\n\n// Readonly controls as plain text\n//\n// Apply class to a readonly input to make it appear like regular plain\n// text (without any border, background color, focus indicator)\n\n.form-control-plaintext {\n  display: block;\n  width: 100%;\n  padding: $input-padding-y 0;\n  margin-bottom: 0; // match inputs if this class comes on inputs with default margins\n  line-height: $input-line-height;\n  color: $input-plaintext-color;\n  background-color: transparent;\n  border: solid transparent;\n  border-width: $input-border-width 0;\n\n  &:focus {\n    outline: 0;\n  }\n\n  &.form-control-sm,\n  &.form-control-lg {\n    padding-right: 0;\n    padding-left: 0;\n  }\n}\n\n// Form control sizing\n//\n// Build on `.form-control` with modifier classes to decrease or increase the\n// height and font-size of form controls.\n//\n// Repeated in `_input_group.scss` to avoid Sass extend issues.\n\n.form-control-sm {\n  min-height: $input-height-sm;\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n\n  &::file-selector-button {\n    padding: $input-padding-y-sm $input-padding-x-sm;\n    margin: (-$input-padding-y-sm) (-$input-padding-x-sm);\n    margin-inline-end: $input-padding-x-sm;\n  }\n}\n\n.form-control-lg {\n  min-height: $input-height-lg;\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n\n  &::file-selector-button {\n    padding: $input-padding-y-lg $input-padding-x-lg;\n    margin: (-$input-padding-y-lg) (-$input-padding-x-lg);\n    margin-inline-end: $input-padding-x-lg;\n  }\n}\n\n// Make sure textareas don't shrink too much when resized\n// https://github.com/twbs/bootstrap/pull/29124\n// stylelint-disable selector-no-qualifying-type\ntextarea {\n  &.form-control {\n    min-height: $input-height;\n  }\n\n  &.form-control-sm {\n    min-height: $input-height-sm;\n  }\n\n  &.form-control-lg {\n    min-height: $input-height-lg;\n  }\n}\n// stylelint-enable selector-no-qualifying-type\n\n.form-control-color {\n  width: $form-color-width;\n  height: $input-height;\n  padding: $input-padding-y;\n\n  &:not(:disabled):not([readonly]) {\n    cursor: pointer;\n  }\n\n  &::-moz-color-swatch {\n    border: 0 !important; // stylelint-disable-line declaration-no-important\n    @include border-radius($input-border-radius);\n  }\n\n  &::-webkit-color-swatch {\n    border: 0 !important; // stylelint-disable-line declaration-no-important\n    @include border-radius($input-border-radius);\n  }\n\n  &.form-control-sm { height: $input-height-sm; }\n  &.form-control-lg { height: $input-height-lg; }\n}\n", "// stylelint-disable property-disallowed-list\n@mixin transition($transition...) {\n  @if length($transition) == 0 {\n    $transition: $transition-base;\n  }\n\n  @if length($transition) > 1 {\n    @each $value in $transition {\n      @if $value == null or $value == none {\n        @warn \"The keyword 'none' or 'null' must be used as a single argument.\";\n      }\n    }\n  }\n\n  @if $enable-transitions {\n    @if nth($transition, 1) != null {\n      transition: $transition;\n    }\n\n    @if $enable-reduced-motion and nth($transition, 1) != null and nth($transition, 1) != none {\n      @media (prefers-reduced-motion: reduce) {\n        transition: none;\n      }\n    }\n  }\n}\n", "// Gradients\n\n// scss-docs-start gradient-bg-mixin\n@mixin gradient-bg($color: null) {\n  background-color: $color;\n\n  @if $enable-gradients {\n    background-image: var(--#{$prefix}gradient);\n  }\n}\n// scss-docs-end gradient-bg-mixin\n\n// scss-docs-start gradient-mixins\n// Horizontal gradient, from left to right\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-x($start-color: $gray-700, $end-color: $gray-800, $start-percent: 0%, $end-percent: 100%) {\n  background-image: linear-gradient(to right, $start-color $start-percent, $end-color $end-percent);\n}\n\n// Vertical gradient, from top to bottom\n//\n// Creates two color stops, start and end, by specifying a color and position for each color stop.\n@mixin gradient-y($start-color: $gray-700, $end-color: $gray-800, $start-percent: null, $end-percent: null) {\n  background-image: linear-gradient(to bottom, $start-color $start-percent, $end-color $end-percent);\n}\n\n@mixin gradient-directional($start-color: $gray-700, $end-color: $gray-800, $deg: 45deg) {\n  background-image: linear-gradient($deg, $start-color, $end-color);\n}\n\n@mixin gradient-x-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient(to right, $start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-y-three-colors($start-color: $blue, $mid-color: $purple, $color-stop: 50%, $end-color: $red) {\n  background-image: linear-gradient($start-color, $mid-color $color-stop, $end-color);\n}\n\n@mixin gradient-radial($inner-color: $gray-700, $outer-color: $gray-800) {\n  background-image: radial-gradient(circle, $inner-color, $outer-color);\n}\n\n@mixin gradient-striped($color: rgba($white, .15), $angle: 45deg) {\n  background-image: linear-gradient($angle, $color 25%, transparent 25%, transparent 50%, $color 50%, $color 75%, transparent 75%, transparent);\n}\n// scss-docs-end gradient-mixins\n", "// Select\n//\n// Replaces the browser default select with a custom one, mostly pulled from\n// https://primer.github.io/.\n\n.form-select {\n  --#{$prefix}form-select-bg-img: #{escape-svg($form-select-indicator)};\n\n  display: block;\n  width: 100%;\n  padding: $form-select-padding-y $form-select-indicator-padding $form-select-padding-y $form-select-padding-x;\n  font-family: $form-select-font-family;\n  @include font-size($form-select-font-size);\n  font-weight: $form-select-font-weight;\n  line-height: $form-select-line-height;\n  color: $form-select-color;\n  background-color: $form-select-bg;\n  background-image: var(--#{$prefix}form-select-bg-img), var(--#{$prefix}form-select-bg-icon, none);\n  background-repeat: no-repeat;\n  background-position: $form-select-bg-position;\n  background-size: $form-select-bg-size;\n  border: $form-select-border-width solid $form-select-border-color;\n  @include border-radius($form-select-border-radius, 0);\n  @include box-shadow($form-select-box-shadow);\n  @include transition($form-select-transition);\n  appearance: none;\n\n  &:focus {\n    border-color: $form-select-focus-border-color;\n    outline: 0;\n    @if $enable-shadows {\n      @include box-shadow($form-select-box-shadow, $form-select-focus-box-shadow);\n    } @else {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      box-shadow: $form-select-focus-box-shadow;\n    }\n  }\n\n  &[multiple],\n  &[size]:not([size=\"1\"]) {\n    padding-right: $form-select-padding-x;\n    background-image: none;\n  }\n\n  &:disabled {\n    color: $form-select-disabled-color;\n    background-color: $form-select-disabled-bg;\n    border-color: $form-select-disabled-border-color;\n  }\n\n  // Remove outline from select box in FF\n  &:-moz-focusring {\n    color: transparent;\n    text-shadow: 0 0 0 $form-select-color;\n  }\n}\n\n.form-select-sm {\n  padding-top: $form-select-padding-y-sm;\n  padding-bottom: $form-select-padding-y-sm;\n  padding-left: $form-select-padding-x-sm;\n  @include font-size($form-select-font-size-sm);\n  @include border-radius($form-select-border-radius-sm);\n}\n\n.form-select-lg {\n  padding-top: $form-select-padding-y-lg;\n  padding-bottom: $form-select-padding-y-lg;\n  padding-left: $form-select-padding-x-lg;\n  @include font-size($form-select-font-size-lg);\n  @include border-radius($form-select-border-radius-lg);\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .form-select {\n      --#{$prefix}form-select-bg-img: #{escape-svg($form-select-indicator-dark)};\n    }\n  }\n}\n", "//\n// Check/radio\n//\n\n.form-check {\n  display: block;\n  min-height: $form-check-min-height;\n  padding-left: $form-check-padding-start;\n  margin-bottom: $form-check-margin-bottom;\n\n  .form-check-input {\n    float: left;\n    margin-left: $form-check-padding-start * -1;\n  }\n}\n\n.form-check-reverse {\n  padding-right: $form-check-padding-start;\n  padding-left: 0;\n  text-align: right;\n\n  .form-check-input {\n    float: right;\n    margin-right: $form-check-padding-start * -1;\n    margin-left: 0;\n  }\n}\n\n.form-check-input {\n  --#{$prefix}form-check-bg: #{$form-check-input-bg};\n\n  width: $form-check-input-width;\n  height: $form-check-input-width;\n  margin-top: ($line-height-base - $form-check-input-width) * .5; // line-height minus check height\n  vertical-align: top;\n  background-color: var(--#{$prefix}form-check-bg);\n  background-image: var(--#{$prefix}form-check-bg-image);\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: contain;\n  border: $form-check-input-border;\n  appearance: none;\n  print-color-adjust: exact; // Keep themed appearance for print\n  @include transition($form-check-transition);\n\n  &[type=\"checkbox\"] {\n    @include border-radius($form-check-input-border-radius);\n  }\n\n  &[type=\"radio\"] {\n    // stylelint-disable-next-line property-disallowed-list\n    border-radius: $form-check-radio-border-radius;\n  }\n\n  &:active {\n    filter: $form-check-input-active-filter;\n  }\n\n  &:focus {\n    border-color: $form-check-input-focus-border;\n    outline: 0;\n    box-shadow: $form-check-input-focus-box-shadow;\n  }\n\n  &:checked {\n    background-color: $form-check-input-checked-bg-color;\n    border-color: $form-check-input-checked-border-color;\n\n    &[type=\"checkbox\"] {\n      @if $enable-gradients {\n        --#{$prefix}form-check-bg-image: #{escape-svg($form-check-input-checked-bg-image)}, var(--#{$prefix}gradient);\n      } @else {\n        --#{$prefix}form-check-bg-image: #{escape-svg($form-check-input-checked-bg-image)};\n      }\n    }\n\n    &[type=\"radio\"] {\n      @if $enable-gradients {\n        --#{$prefix}form-check-bg-image: #{escape-svg($form-check-radio-checked-bg-image)}, var(--#{$prefix}gradient);\n      } @else {\n        --#{$prefix}form-check-bg-image: #{escape-svg($form-check-radio-checked-bg-image)};\n      }\n    }\n  }\n\n  &[type=\"checkbox\"]:indeterminate {\n    background-color: $form-check-input-indeterminate-bg-color;\n    border-color: $form-check-input-indeterminate-border-color;\n\n    @if $enable-gradients {\n      --#{$prefix}form-check-bg-image: #{escape-svg($form-check-input-indeterminate-bg-image)}, var(--#{$prefix}gradient);\n    } @else {\n      --#{$prefix}form-check-bg-image: #{escape-svg($form-check-input-indeterminate-bg-image)};\n    }\n  }\n\n  &:disabled {\n    pointer-events: none;\n    filter: none;\n    opacity: $form-check-input-disabled-opacity;\n  }\n\n  // Use disabled attribute in addition of :disabled pseudo-class\n  // See: https://github.com/twbs/bootstrap/issues/28247\n  &[disabled],\n  &:disabled {\n    ~ .form-check-label {\n      cursor: default;\n      opacity: $form-check-label-disabled-opacity;\n    }\n  }\n}\n\n.form-check-label {\n  color: $form-check-label-color;\n  cursor: $form-check-label-cursor;\n}\n\n//\n// Switch\n//\n\n.form-switch {\n  padding-left: $form-switch-padding-start;\n\n  .form-check-input {\n    --#{$prefix}form-switch-bg: #{escape-svg($form-switch-bg-image)};\n\n    width: $form-switch-width;\n    margin-left: $form-switch-padding-start * -1;\n    background-image: var(--#{$prefix}form-switch-bg);\n    background-position: left center;\n    @include border-radius($form-switch-border-radius);\n    @include transition($form-switch-transition);\n\n    &:focus {\n      --#{$prefix}form-switch-bg: #{escape-svg($form-switch-focus-bg-image)};\n    }\n\n    &:checked {\n      background-position: $form-switch-checked-bg-position;\n\n      @if $enable-gradients {\n        --#{$prefix}form-switch-bg: #{escape-svg($form-switch-checked-bg-image)}, var(--#{$prefix}gradient);\n      } @else {\n        --#{$prefix}form-switch-bg: #{escape-svg($form-switch-checked-bg-image)};\n      }\n    }\n  }\n\n  &.form-check-reverse {\n    padding-right: $form-switch-padding-start;\n    padding-left: 0;\n\n    .form-check-input {\n      margin-right: $form-switch-padding-start * -1;\n      margin-left: 0;\n    }\n  }\n}\n\n.form-check-inline {\n  display: inline-block;\n  margin-right: $form-check-inline-margin-end;\n}\n\n.btn-check {\n  position: absolute;\n  clip: rect(0, 0, 0, 0);\n  pointer-events: none;\n\n  &[disabled],\n  &:disabled {\n    + .btn {\n      pointer-events: none;\n      filter: none;\n      opacity: $form-check-btn-check-disabled-opacity;\n    }\n  }\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .form-switch .form-check-input:not(:checked):not(:focus) {\n      --#{$prefix}form-switch-bg: #{escape-svg($form-switch-bg-image-dark)};\n    }\n  }\n}\n", "// Range\n//\n// Style range inputs the same across browsers. Vendor-specific rules for pseudo\n// elements cannot be mixed. As such, there are no shared styles for focus or\n// active states on prefixed selectors.\n\n.form-range {\n  width: 100%;\n  height: add($form-range-thumb-height, $form-range-thumb-focus-box-shadow-width * 2);\n  padding: 0; // Need to reset padding\n  background-color: transparent;\n  appearance: none;\n\n  &:focus {\n    outline: 0;\n\n    // Pseudo-elements must be split across multiple rulesets to have an effect.\n    // No box-shadow() mixin for focus accessibility.\n    &::-webkit-slider-thumb { box-shadow: $form-range-thumb-focus-box-shadow; }\n    &::-moz-range-thumb     { box-shadow: $form-range-thumb-focus-box-shadow; }\n  }\n\n  &::-moz-focus-outer {\n    border: 0;\n  }\n\n  &::-webkit-slider-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    margin-top: ($form-range-track-height - $form-range-thumb-height) * .5; // Webkit specific\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-webkit-slider-runnable-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent; // Why?\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent;\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &::-moz-range-thumb {\n    width: $form-range-thumb-width;\n    height: $form-range-thumb-height;\n    @include gradient-bg($form-range-thumb-bg);\n    border: $form-range-thumb-border;\n    @include border-radius($form-range-thumb-border-radius);\n    @include box-shadow($form-range-thumb-box-shadow);\n    @include transition($form-range-thumb-transition);\n    appearance: none;\n\n    &:active {\n      @include gradient-bg($form-range-thumb-active-bg);\n    }\n  }\n\n  &::-moz-range-track {\n    width: $form-range-track-width;\n    height: $form-range-track-height;\n    color: transparent;\n    cursor: $form-range-track-cursor;\n    background-color: $form-range-track-bg;\n    border-color: transparent; // Firefox specific?\n    @include border-radius($form-range-track-border-radius);\n    @include box-shadow($form-range-track-box-shadow);\n  }\n\n  &:disabled {\n    pointer-events: none;\n\n    &::-webkit-slider-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n\n    &::-moz-range-thumb {\n      background-color: $form-range-thumb-disabled-bg;\n    }\n  }\n}\n", ".form-floating {\n  position: relative;\n\n  > .form-control,\n  > .form-control-plaintext,\n  > .form-select {\n    height: $form-floating-height;\n    min-height: $form-floating-height;\n    line-height: $form-floating-line-height;\n  }\n\n  > label {\n    position: absolute;\n    top: 0;\n    left: 0;\n    z-index: 2;\n    height: 100%; // allow textareas\n    padding: $form-floating-padding-y $form-floating-padding-x;\n    overflow: hidden;\n    text-align: start;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    pointer-events: none;\n    border: $input-border-width solid transparent; // Required for aligning label's text with the input as it affects inner box model\n    transform-origin: 0 0;\n    @include transition($form-floating-transition);\n  }\n\n  > .form-control,\n  > .form-control-plaintext {\n    padding: $form-floating-padding-y $form-floating-padding-x;\n\n    &::placeholder {\n      color: transparent;\n    }\n\n    &:focus,\n    &:not(:placeholder-shown) {\n      padding-top: $form-floating-input-padding-t;\n      padding-bottom: $form-floating-input-padding-b;\n    }\n    // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n    &:-webkit-autofill {\n      padding-top: $form-floating-input-padding-t;\n      padding-bottom: $form-floating-input-padding-b;\n    }\n  }\n\n  > .form-select {\n    padding-top: $form-floating-input-padding-t;\n    padding-bottom: $form-floating-input-padding-b;\n  }\n\n  > .form-control:focus,\n  > .form-control:not(:placeholder-shown),\n  > .form-control-plaintext,\n  > .form-select {\n    ~ label {\n      color: rgba(var(--#{$prefix}body-color-rgb), #{$form-floating-label-opacity});\n      transform: $form-floating-label-transform;\n\n      &::after {\n        position: absolute;\n        inset: $form-floating-padding-y ($form-floating-padding-x * .5);\n        z-index: -1;\n        height: $form-floating-label-height;\n        content: \"\";\n        background-color: $input-bg;\n        @include border-radius($input-border-radius);\n      }\n    }\n  }\n  // Duplicated because `:-webkit-autofill` invalidates other selectors when grouped\n  > .form-control:-webkit-autofill {\n    ~ label {\n      color: rgba(var(--#{$prefix}body-color-rgb), #{$form-floating-label-opacity});\n      transform: $form-floating-label-transform;\n    }\n  }\n\n  > .form-control-plaintext {\n    ~ label {\n      border-width: $input-border-width 0; // Required to properly position label text - as explained above\n    }\n  }\n\n  > :disabled ~ label {\n    color: $form-floating-label-disabled-color;\n\n    &::after {\n      background-color: $input-disabled-bg;\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // For form validation feedback\n  align-items: stretch;\n  width: 100%;\n\n  > .form-control,\n  > .form-select,\n  > .form-floating {\n    position: relative; // For focus state's z-index\n    flex: 1 1 auto;\n    width: 1%;\n    min-width: 0; // https://stackoverflow.com/questions/36247140/why-dont-flex-items-shrink-past-content-size\n  }\n\n  // Bring the \"active\" form control to the top of surrounding elements\n  > .form-control:focus,\n  > .form-select:focus,\n  > .form-floating:focus-within {\n    z-index: 5;\n  }\n\n  // Ensure buttons are always above inputs for more visually pleasing borders.\n  // This isn't needed for `.input-group-text` since it shares the same border-color\n  // as our inputs.\n  .btn {\n    position: relative;\n    z-index: 2;\n\n    &:focus {\n      z-index: 5;\n    }\n  }\n}\n\n\n// Textual addons\n//\n// Serves as a catch-all element for any text or radio/checkbox input you wish\n// to prepend or append to an input.\n\n.input-group-text {\n  display: flex;\n  align-items: center;\n  padding: $input-group-addon-padding-y $input-group-addon-padding-x;\n  @include font-size($input-font-size); // Match inputs\n  font-weight: $input-group-addon-font-weight;\n  line-height: $input-line-height;\n  color: $input-group-addon-color;\n  text-align: center;\n  white-space: nowrap;\n  background-color: $input-group-addon-bg;\n  border: $input-border-width solid $input-group-addon-border-color;\n  @include border-radius($input-border-radius);\n}\n\n\n// Sizing\n//\n// Remix the default form control sizing classes into new ones for easier\n// manipulation.\n\n.input-group-lg > .form-control,\n.input-group-lg > .form-select,\n.input-group-lg > .input-group-text,\n.input-group-lg > .btn {\n  padding: $input-padding-y-lg $input-padding-x-lg;\n  @include font-size($input-font-size-lg);\n  @include border-radius($input-border-radius-lg);\n}\n\n.input-group-sm > .form-control,\n.input-group-sm > .form-select,\n.input-group-sm > .input-group-text,\n.input-group-sm > .btn {\n  padding: $input-padding-y-sm $input-padding-x-sm;\n  @include font-size($input-font-size-sm);\n  @include border-radius($input-border-radius-sm);\n}\n\n.input-group-lg > .form-select,\n.input-group-sm > .form-select {\n  padding-right: $form-select-padding-x + $form-select-indicator-padding;\n}\n\n\n// Rounded corners\n//\n// These rulesets must come after the sizing ones to properly override sm and lg\n// border-radius values when extending. They're more specific than we'd like\n// with the `.input-group >` part, but without it, we cannot override the sizing.\n\n// stylelint-disable-next-line no-duplicate-selectors\n.input-group {\n  &:not(.has-validation) {\n    > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),\n    > .dropdown-toggle:nth-last-child(n + 3),\n    > .form-floating:not(:last-child) > .form-control,\n    > .form-floating:not(:last-child) > .form-select {\n      @include border-end-radius(0);\n    }\n  }\n\n  &.has-validation {\n    > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),\n    > .dropdown-toggle:nth-last-child(n + 4),\n    > .form-floating:nth-last-child(n + 3) > .form-control,\n    > .form-floating:nth-last-child(n + 3) > .form-select {\n      @include border-end-radius(0);\n    }\n  }\n\n  $validation-messages: \"\";\n  @each $state in map-keys($form-validation-states) {\n    $validation-messages: $validation-messages + \":not(.\" + unquote($state) + \"-tooltip)\" + \":not(.\" + unquote($state) + \"-feedback)\";\n  }\n\n  > :not(:first-child):not(.dropdown-menu)#{$validation-messages} {\n    margin-left: calc(#{$input-border-width} * -1); // stylelint-disable-line function-disallowed-list\n    @include border-start-radius(0);\n  }\n\n  > .form-floating:not(:first-child) > .form-control,\n  > .form-floating:not(:first-child) > .form-select {\n    @include border-start-radius(0);\n  }\n}\n", "// This mixin uses an `if()` technique to be compatible with Dart Sass\n// See https://github.com/sass/sass/issues/1873#issuecomment-152293725 for more details\n\n// scss-docs-start form-validation-mixins\n@mixin form-validation-state-selector($state) {\n  @if ($state == \"valid\" or $state == \"invalid\") {\n    .was-validated #{if(&, \"&\", \"\")}:#{$state},\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  } @else {\n    #{if(&, \"&\", \"\")}.is-#{$state} {\n      @content;\n    }\n  }\n}\n\n@mixin form-validation-state(\n  $state,\n  $color,\n  $icon,\n  $tooltip-color: color-contrast($color),\n  $tooltip-bg-color: rgba($color, $form-feedback-tooltip-opacity),\n  $focus-box-shadow: 0 0 $input-btn-focus-blur $input-focus-width rgba($color, $input-btn-focus-color-opacity),\n  $border-color: $color\n) {\n  .#{$state}-feedback {\n    display: none;\n    width: 100%;\n    margin-top: $form-feedback-margin-top;\n    @include font-size($form-feedback-font-size);\n    font-style: $form-feedback-font-style;\n    color: $color;\n  }\n\n  .#{$state}-tooltip {\n    position: absolute;\n    top: 100%;\n    z-index: 5;\n    display: none;\n    max-width: 100%; // Contain to parent when possible\n    padding: $form-feedback-tooltip-padding-y $form-feedback-tooltip-padding-x;\n    margin-top: .1rem;\n    @include font-size($form-feedback-tooltip-font-size);\n    line-height: $form-feedback-tooltip-line-height;\n    color: $tooltip-color;\n    background-color: $tooltip-bg-color;\n    @include border-radius($form-feedback-tooltip-border-radius);\n  }\n\n  @include form-validation-state-selector($state) {\n    ~ .#{$state}-feedback,\n    ~ .#{$state}-tooltip {\n      display: block;\n    }\n  }\n\n  .form-control {\n    @include form-validation-state-selector($state) {\n      border-color: $border-color;\n\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-image: escape-svg($icon);\n        background-repeat: no-repeat;\n        background-position: right $input-height-inner-quarter center;\n        background-size: $input-height-inner-half $input-height-inner-half;\n      }\n\n      &:focus {\n        border-color: $border-color;\n        box-shadow: $focus-box-shadow;\n      }\n    }\n  }\n\n  // stylelint-disable-next-line selector-no-qualifying-type\n  textarea.form-control {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        padding-right: $input-height-inner;\n        background-position: top $input-height-inner-quarter right $input-height-inner-quarter;\n      }\n    }\n  }\n\n  .form-select {\n    @include form-validation-state-selector($state) {\n      border-color: $border-color;\n\n      @if $enable-validation-icons {\n        &:not([multiple]):not([size]),\n        &:not([multiple])[size=\"1\"] {\n          --#{$prefix}form-select-bg-icon: #{escape-svg($icon)};\n          padding-right: $form-select-feedback-icon-padding-end;\n          background-position: $form-select-bg-position, $form-select-feedback-icon-position;\n          background-size: $form-select-bg-size, $form-select-feedback-icon-size;\n        }\n      }\n\n      &:focus {\n        border-color: $border-color;\n        box-shadow: $focus-box-shadow;\n      }\n    }\n  }\n\n  .form-control-color {\n    @include form-validation-state-selector($state) {\n      @if $enable-validation-icons {\n        width: add($form-color-width, $input-height-inner);\n      }\n    }\n  }\n\n  .form-check-input {\n    @include form-validation-state-selector($state) {\n      border-color: $border-color;\n\n      &:checked {\n        background-color: $color;\n      }\n\n      &:focus {\n        box-shadow: $focus-box-shadow;\n      }\n\n      ~ .form-check-label {\n        color: $color;\n      }\n    }\n  }\n  .form-check-inline .form-check-input {\n    ~ .#{$state}-feedback {\n      margin-left: .5em;\n    }\n  }\n\n  .input-group {\n    > .form-control:not(:focus),\n    > .form-select:not(:focus),\n    > .form-floating:not(:focus-within) {\n      @include form-validation-state-selector($state) {\n        @if $state == \"valid\" {\n          z-index: 3;\n        } @else if $state == \"invalid\" {\n          z-index: 4;\n        }\n      }\n    }\n  }\n}\n// scss-docs-end form-validation-mixins\n", "//\n// Base styles\n//\n\n.btn {\n  // scss-docs-start btn-css-vars\n  --#{$prefix}btn-padding-x: #{$btn-padding-x};\n  --#{$prefix}btn-padding-y: #{$btn-padding-y};\n  --#{$prefix}btn-font-family: #{$btn-font-family};\n  @include rfs($btn-font-size, --#{$prefix}btn-font-size);\n  --#{$prefix}btn-font-weight: #{$btn-font-weight};\n  --#{$prefix}btn-line-height: #{$btn-line-height};\n  --#{$prefix}btn-color: #{$btn-color};\n  --#{$prefix}btn-bg: transparent;\n  --#{$prefix}btn-border-width: #{$btn-border-width};\n  --#{$prefix}btn-border-color: transparent;\n  --#{$prefix}btn-border-radius: #{$btn-border-radius};\n  --#{$prefix}btn-hover-border-color: transparent;\n  --#{$prefix}btn-box-shadow: #{$btn-box-shadow};\n  --#{$prefix}btn-disabled-opacity: #{$btn-disabled-opacity};\n  --#{$prefix}btn-focus-box-shadow: 0 0 0 #{$btn-focus-width} rgba(var(--#{$prefix}btn-focus-shadow-rgb), .5);\n  // scss-docs-end btn-css-vars\n\n  display: inline-block;\n  padding: var(--#{$prefix}btn-padding-y) var(--#{$prefix}btn-padding-x);\n  font-family: var(--#{$prefix}btn-font-family);\n  @include font-size(var(--#{$prefix}btn-font-size));\n  font-weight: var(--#{$prefix}btn-font-weight);\n  line-height: var(--#{$prefix}btn-line-height);\n  color: var(--#{$prefix}btn-color);\n  text-align: center;\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: $btn-white-space;\n  vertical-align: middle;\n  cursor: if($enable-button-pointers, pointer, null);\n  user-select: none;\n  border: var(--#{$prefix}btn-border-width) solid var(--#{$prefix}btn-border-color);\n  @include border-radius(var(--#{$prefix}btn-border-radius));\n  @include gradient-bg(var(--#{$prefix}btn-bg));\n  @include box-shadow(var(--#{$prefix}btn-box-shadow));\n  @include transition($btn-transition);\n\n  &:hover {\n    color: var(--#{$prefix}btn-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    background-color: var(--#{$prefix}btn-hover-bg);\n    border-color: var(--#{$prefix}btn-hover-border-color);\n  }\n\n  .btn-check + &:hover {\n    // override for the checkbox/radio buttons\n    color: var(--#{$prefix}btn-color);\n    background-color: var(--#{$prefix}btn-bg);\n    border-color: var(--#{$prefix}btn-border-color);\n  }\n\n  &:focus-visible {\n    color: var(--#{$prefix}btn-hover-color);\n    @include gradient-bg(var(--#{$prefix}btn-hover-bg));\n    border-color: var(--#{$prefix}btn-hover-border-color);\n    outline: 0;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: var(--#{$prefix}btn-box-shadow), var(--#{$prefix}btn-focus-box-shadow);\n    } @else {\n      box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n    }\n  }\n\n  .btn-check:focus-visible + & {\n    border-color: var(--#{$prefix}btn-hover-border-color);\n    outline: 0;\n    // Avoid using mixin so we can pass custom focus shadow properly\n    @if $enable-shadows {\n      box-shadow: var(--#{$prefix}btn-box-shadow), var(--#{$prefix}btn-focus-box-shadow);\n    } @else {\n      box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n    }\n  }\n\n  .btn-check:checked + &,\n  :not(.btn-check) + &:active,\n  &:first-child:active,\n  &.active,\n  &.show {\n    color: var(--#{$prefix}btn-active-color);\n    background-color: var(--#{$prefix}btn-active-bg);\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n    border-color: var(--#{$prefix}btn-active-border-color);\n    @include box-shadow(var(--#{$prefix}btn-active-shadow));\n\n    &:focus-visible {\n      // Avoid using mixin so we can pass custom focus shadow properly\n      @if $enable-shadows {\n        box-shadow: var(--#{$prefix}btn-active-shadow), var(--#{$prefix}btn-focus-box-shadow);\n      } @else {\n        box-shadow: var(--#{$prefix}btn-focus-box-shadow);\n      }\n    }\n  }\n\n  &:disabled,\n  &.disabled,\n  fieldset:disabled & {\n    color: var(--#{$prefix}btn-disabled-color);\n    pointer-events: none;\n    background-color: var(--#{$prefix}btn-disabled-bg);\n    background-image: if($enable-gradients, none, null);\n    border-color: var(--#{$prefix}btn-disabled-border-color);\n    opacity: var(--#{$prefix}btn-disabled-opacity);\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Alternate buttons\n//\n\n// scss-docs-start btn-variant-loops\n@each $color, $value in $theme-colors {\n  .btn-#{$color} {\n    @if $color == \"light\" {\n      @include button-variant(\n        $value,\n        $value,\n        $hover-background: shade-color($value, $btn-hover-bg-shade-amount),\n        $hover-border: shade-color($value, $btn-hover-border-shade-amount),\n        $active-background: shade-color($value, $btn-active-bg-shade-amount),\n        $active-border: shade-color($value, $btn-active-border-shade-amount)\n      );\n    } @else if $color == \"dark\" {\n      @include button-variant(\n        $value,\n        $value,\n        $hover-background: tint-color($value, $btn-hover-bg-tint-amount),\n        $hover-border: tint-color($value, $btn-hover-border-tint-amount),\n        $active-background: tint-color($value, $btn-active-bg-tint-amount),\n        $active-border: tint-color($value, $btn-active-border-tint-amount)\n      );\n    } @else {\n      @include button-variant($value, $value);\n    }\n  }\n}\n\n@each $color, $value in $theme-colors {\n  .btn-outline-#{$color} {\n    @include button-outline-variant($value);\n  }\n}\n// scss-docs-end btn-variant-loops\n\n\n//\n// Link buttons\n//\n\n// Make a button look and behave like a link\n.btn-link {\n  --#{$prefix}btn-font-weight: #{$font-weight-normal};\n  --#{$prefix}btn-color: #{$btn-link-color};\n  --#{$prefix}btn-bg: transparent;\n  --#{$prefix}btn-border-color: transparent;\n  --#{$prefix}btn-hover-color: #{$btn-link-hover-color};\n  --#{$prefix}btn-hover-border-color: transparent;\n  --#{$prefix}btn-active-color: #{$btn-link-hover-color};\n  --#{$prefix}btn-active-border-color: transparent;\n  --#{$prefix}btn-disabled-color: #{$btn-link-disabled-color};\n  --#{$prefix}btn-disabled-border-color: transparent;\n  --#{$prefix}btn-box-shadow: 0 0 0 #000; // Can't use `none` as keyword negates all values when used with multiple shadows\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb(mix(color-contrast($link-color), $link-color, 15%))};\n\n  text-decoration: $link-decoration;\n  @if $enable-gradients {\n    background-image: none;\n  }\n\n  &:hover,\n  &:focus-visible {\n    text-decoration: $link-hover-decoration;\n  }\n\n  &:focus-visible {\n    color: var(--#{$prefix}btn-color);\n  }\n\n  &:hover {\n    color: var(--#{$prefix}btn-hover-color);\n  }\n\n  // No need for an active state here\n}\n\n\n//\n// Button Sizes\n//\n\n.btn-lg {\n  @include button-size($btn-padding-y-lg, $btn-padding-x-lg, $btn-font-size-lg, $btn-border-radius-lg);\n}\n\n.btn-sm {\n  @include button-size($btn-padding-y-sm, $btn-padding-x-sm, $btn-font-size-sm, $btn-border-radius-sm);\n}\n", "// Button variants\n//\n// Easily pump out default styles, as well as :hover, :focus, :active,\n// and disabled options for all buttons\n\n// scss-docs-start btn-variant-mixin\n@mixin button-variant(\n  $background,\n  $border,\n  $color: color-contrast($background),\n  $hover-background: if($color == $color-contrast-light, shade-color($background, $btn-hover-bg-shade-amount), tint-color($background, $btn-hover-bg-tint-amount)),\n  $hover-border: if($color == $color-contrast-light, shade-color($border, $btn-hover-border-shade-amount), tint-color($border, $btn-hover-border-tint-amount)),\n  $hover-color: color-contrast($hover-background),\n  $active-background: if($color == $color-contrast-light, shade-color($background, $btn-active-bg-shade-amount), tint-color($background, $btn-active-bg-tint-amount)),\n  $active-border: if($color == $color-contrast-light, shade-color($border, $btn-active-border-shade-amount), tint-color($border, $btn-active-border-tint-amount)),\n  $active-color: color-contrast($active-background),\n  $disabled-background: $background,\n  $disabled-border: $border,\n  $disabled-color: color-contrast($disabled-background)\n) {\n  --#{$prefix}btn-color: #{$color};\n  --#{$prefix}btn-bg: #{$background};\n  --#{$prefix}btn-border-color: #{$border};\n  --#{$prefix}btn-hover-color: #{$hover-color};\n  --#{$prefix}btn-hover-bg: #{$hover-background};\n  --#{$prefix}btn-hover-border-color: #{$hover-border};\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb(mix($color, $border, 15%))};\n  --#{$prefix}btn-active-color: #{$active-color};\n  --#{$prefix}btn-active-bg: #{$active-background};\n  --#{$prefix}btn-active-border-color: #{$active-border};\n  --#{$prefix}btn-active-shadow: #{$btn-active-box-shadow};\n  --#{$prefix}btn-disabled-color: #{$disabled-color};\n  --#{$prefix}btn-disabled-bg: #{$disabled-background};\n  --#{$prefix}btn-disabled-border-color: #{$disabled-border};\n}\n// scss-docs-end btn-variant-mixin\n\n// scss-docs-start btn-outline-variant-mixin\n@mixin button-outline-variant(\n  $color,\n  $color-hover: color-contrast($color),\n  $active-background: $color,\n  $active-border: $color,\n  $active-color: color-contrast($active-background)\n) {\n  --#{$prefix}btn-color: #{$color};\n  --#{$prefix}btn-border-color: #{$color};\n  --#{$prefix}btn-hover-color: #{$color-hover};\n  --#{$prefix}btn-hover-bg: #{$active-background};\n  --#{$prefix}btn-hover-border-color: #{$active-border};\n  --#{$prefix}btn-focus-shadow-rgb: #{to-rgb($color)};\n  --#{$prefix}btn-active-color: #{$active-color};\n  --#{$prefix}btn-active-bg: #{$active-background};\n  --#{$prefix}btn-active-border-color: #{$active-border};\n  --#{$prefix}btn-active-shadow: #{$btn-active-box-shadow};\n  --#{$prefix}btn-disabled-color: #{$color};\n  --#{$prefix}btn-disabled-bg: transparent;\n  --#{$prefix}btn-disabled-border-color: #{$color};\n  --#{$prefix}gradient: none;\n}\n// scss-docs-end btn-outline-variant-mixin\n\n// scss-docs-start btn-size-mixin\n@mixin button-size($padding-y, $padding-x, $font-size, $border-radius) {\n  --#{$prefix}btn-padding-y: #{$padding-y};\n  --#{$prefix}btn-padding-x: #{$padding-x};\n  @include rfs($font-size, --#{$prefix}btn-font-size);\n  --#{$prefix}btn-border-radius: #{$border-radius};\n}\n// scss-docs-end btn-size-mixin\n", ".fade {\n  @include transition($transition-fade);\n\n  &:not(.show) {\n    opacity: 0;\n  }\n}\n\n// scss-docs-start collapse-classes\n.collapse {\n  &:not(.show) {\n    display: none;\n  }\n}\n\n.collapsing {\n  height: 0;\n  overflow: hidden;\n  @include transition($transition-collapse);\n\n  &.collapse-horizontal {\n    width: 0;\n    height: auto;\n    @include transition($transition-collapse-width);\n  }\n}\n// scss-docs-end collapse-classes\n", "// The dropdown wrapper (`<div>`)\n.dropup,\n.dropend,\n.dropdown,\n.dropstart,\n.dropup-center,\n.dropdown-center {\n  position: relative;\n}\n\n.dropdown-toggle {\n  white-space: nowrap;\n\n  // Generate the caret automatically\n  @include caret();\n}\n\n// The dropdown menu\n.dropdown-menu {\n  // scss-docs-start dropdown-css-vars\n  --#{$prefix}dropdown-zindex: #{$zindex-dropdown};\n  --#{$prefix}dropdown-min-width: #{$dropdown-min-width};\n  --#{$prefix}dropdown-padding-x: #{$dropdown-padding-x};\n  --#{$prefix}dropdown-padding-y: #{$dropdown-padding-y};\n  --#{$prefix}dropdown-spacer: #{$dropdown-spacer};\n  @include rfs($dropdown-font-size, --#{$prefix}dropdown-font-size);\n  --#{$prefix}dropdown-color: #{$dropdown-color};\n  --#{$prefix}dropdown-bg: #{$dropdown-bg};\n  --#{$prefix}dropdown-border-color: #{$dropdown-border-color};\n  --#{$prefix}dropdown-border-radius: #{$dropdown-border-radius};\n  --#{$prefix}dropdown-border-width: #{$dropdown-border-width};\n  --#{$prefix}dropdown-inner-border-radius: #{$dropdown-inner-border-radius};\n  --#{$prefix}dropdown-divider-bg: #{$dropdown-divider-bg};\n  --#{$prefix}dropdown-divider-margin-y: #{$dropdown-divider-margin-y};\n  --#{$prefix}dropdown-box-shadow: #{$dropdown-box-shadow};\n  --#{$prefix}dropdown-link-color: #{$dropdown-link-color};\n  --#{$prefix}dropdown-link-hover-color: #{$dropdown-link-hover-color};\n  --#{$prefix}dropdown-link-hover-bg: #{$dropdown-link-hover-bg};\n  --#{$prefix}dropdown-link-active-color: #{$dropdown-link-active-color};\n  --#{$prefix}dropdown-link-active-bg: #{$dropdown-link-active-bg};\n  --#{$prefix}dropdown-link-disabled-color: #{$dropdown-link-disabled-color};\n  --#{$prefix}dropdown-item-padding-x: #{$dropdown-item-padding-x};\n  --#{$prefix}dropdown-item-padding-y: #{$dropdown-item-padding-y};\n  --#{$prefix}dropdown-header-color: #{$dropdown-header-color};\n  --#{$prefix}dropdown-header-padding-x: #{$dropdown-header-padding-x};\n  --#{$prefix}dropdown-header-padding-y: #{$dropdown-header-padding-y};\n  // scss-docs-end dropdown-css-vars\n\n  position: absolute;\n  z-index: var(--#{$prefix}dropdown-zindex);\n  display: none; // none by default, but block on \"open\" of the menu\n  min-width: var(--#{$prefix}dropdown-min-width);\n  padding: var(--#{$prefix}dropdown-padding-y) var(--#{$prefix}dropdown-padding-x);\n  margin: 0; // Override default margin of ul\n  @include font-size(var(--#{$prefix}dropdown-font-size));\n  color: var(--#{$prefix}dropdown-color);\n  text-align: left; // Ensures proper alignment if parent has it changed (e.g., modal footer)\n  list-style: none;\n  background-color: var(--#{$prefix}dropdown-bg);\n  background-clip: padding-box;\n  border: var(--#{$prefix}dropdown-border-width) solid var(--#{$prefix}dropdown-border-color);\n  @include border-radius(var(--#{$prefix}dropdown-border-radius));\n  @include box-shadow(var(--#{$prefix}dropdown-box-shadow));\n\n  &[data-bs-popper] {\n    top: 100%;\n    left: 0;\n    margin-top: var(--#{$prefix}dropdown-spacer);\n  }\n\n  @if $dropdown-padding-y == 0 {\n    > .dropdown-item:first-child,\n    > li:first-child .dropdown-item {\n      @include border-top-radius(var(--#{$prefix}dropdown-inner-border-radius));\n    }\n    > .dropdown-item:last-child,\n    > li:last-child .dropdown-item {\n      @include border-bottom-radius(var(--#{$prefix}dropdown-inner-border-radius));\n    }\n\n  }\n}\n\n// scss-docs-start responsive-breakpoints\n// We deliberately hardcode the `bs-` prefix because we check\n// this custom property in JS to determine Popper's positioning\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .dropdown-menu#{$infix}-start {\n      --bs-position: start;\n\n      &[data-bs-popper] {\n        right: auto;\n        left: 0;\n      }\n    }\n\n    .dropdown-menu#{$infix}-end {\n      --bs-position: end;\n\n      &[data-bs-popper] {\n        right: 0;\n        left: auto;\n      }\n    }\n  }\n}\n// scss-docs-end responsive-breakpoints\n\n// Allow for dropdowns to go bottom up (aka, dropup-menu)\n// Just add .dropup after the standard .dropdown class and you're set.\n.dropup {\n  .dropdown-menu[data-bs-popper] {\n    top: auto;\n    bottom: 100%;\n    margin-top: 0;\n    margin-bottom: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(up);\n  }\n}\n\n.dropend {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: auto;\n    left: 100%;\n    margin-top: 0;\n    margin-left: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(end);\n    &::after {\n      vertical-align: 0;\n    }\n  }\n}\n\n.dropstart {\n  .dropdown-menu[data-bs-popper] {\n    top: 0;\n    right: 100%;\n    left: auto;\n    margin-top: 0;\n    margin-right: var(--#{$prefix}dropdown-spacer);\n  }\n\n  .dropdown-toggle {\n    @include caret(start);\n    &::before {\n      vertical-align: 0;\n    }\n  }\n}\n\n\n// Dividers (basically an `<hr>`) within the dropdown\n.dropdown-divider {\n  height: 0;\n  margin: var(--#{$prefix}dropdown-divider-margin-y) 0;\n  overflow: hidden;\n  border-top: 1px solid var(--#{$prefix}dropdown-divider-bg);\n  opacity: 1; // Revisit in v6 to de-dupe styles that conflict with <hr> element\n}\n\n// Links, buttons, and more within the dropdown menu\n//\n// `<button>`-specific styles are denoted with `// For <button>s`\n.dropdown-item {\n  display: block;\n  width: 100%; // For `<button>`s\n  padding: var(--#{$prefix}dropdown-item-padding-y) var(--#{$prefix}dropdown-item-padding-x);\n  clear: both;\n  font-weight: $font-weight-normal;\n  color: var(--#{$prefix}dropdown-link-color);\n  text-align: inherit; // For `<button>`s\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: nowrap; // prevent links from randomly breaking onto new lines\n  background-color: transparent; // For `<button>`s\n  border: 0; // For `<button>`s\n  @include border-radius(var(--#{$prefix}dropdown-item-border-radius, 0));\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}dropdown-link-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n    @include gradient-bg(var(--#{$prefix}dropdown-link-hover-bg));\n  }\n\n  &.active,\n  &:active {\n    color: var(--#{$prefix}dropdown-link-active-color);\n    text-decoration: none;\n    @include gradient-bg(var(--#{$prefix}dropdown-link-active-bg));\n  }\n\n  &.disabled,\n  &:disabled {\n    color: var(--#{$prefix}dropdown-link-disabled-color);\n    pointer-events: none;\n    background-color: transparent;\n    // Remove CSS gradients if they're enabled\n    background-image: if($enable-gradients, none, null);\n  }\n}\n\n.dropdown-menu.show {\n  display: block;\n}\n\n// Dropdown section headers\n.dropdown-header {\n  display: block;\n  padding: var(--#{$prefix}dropdown-header-padding-y) var(--#{$prefix}dropdown-header-padding-x);\n  margin-bottom: 0; // for use with heading elements\n  @include font-size($font-size-sm);\n  color: var(--#{$prefix}dropdown-header-color);\n  white-space: nowrap; // as with > li > a\n}\n\n// Dropdown text\n.dropdown-item-text {\n  display: block;\n  padding: var(--#{$prefix}dropdown-item-padding-y) var(--#{$prefix}dropdown-item-padding-x);\n  color: var(--#{$prefix}dropdown-link-color);\n}\n\n// Dark dropdowns\n.dropdown-menu-dark {\n  // scss-docs-start dropdown-dark-css-vars\n  --#{$prefix}dropdown-color: #{$dropdown-dark-color};\n  --#{$prefix}dropdown-bg: #{$dropdown-dark-bg};\n  --#{$prefix}dropdown-border-color: #{$dropdown-dark-border-color};\n  --#{$prefix}dropdown-box-shadow: #{$dropdown-dark-box-shadow};\n  --#{$prefix}dropdown-link-color: #{$dropdown-dark-link-color};\n  --#{$prefix}dropdown-link-hover-color: #{$dropdown-dark-link-hover-color};\n  --#{$prefix}dropdown-divider-bg: #{$dropdown-dark-divider-bg};\n  --#{$prefix}dropdown-link-hover-bg: #{$dropdown-dark-link-hover-bg};\n  --#{$prefix}dropdown-link-active-color: #{$dropdown-dark-link-active-color};\n  --#{$prefix}dropdown-link-active-bg: #{$dropdown-dark-link-active-bg};\n  --#{$prefix}dropdown-link-disabled-color: #{$dropdown-dark-link-disabled-color};\n  --#{$prefix}dropdown-header-color: #{$dropdown-dark-header-color};\n  // scss-docs-end dropdown-dark-css-vars\n}\n", "// scss-docs-start caret-mixins\n@mixin caret-down($width: $caret-width) {\n  border-top: $width solid;\n  border-right: $width solid transparent;\n  border-bottom: 0;\n  border-left: $width solid transparent;\n}\n\n@mixin caret-up($width: $caret-width) {\n  border-top: 0;\n  border-right: $width solid transparent;\n  border-bottom: $width solid;\n  border-left: $width solid transparent;\n}\n\n@mixin caret-end($width: $caret-width) {\n  border-top: $width solid transparent;\n  border-right: 0;\n  border-bottom: $width solid transparent;\n  border-left: $width solid;\n}\n\n@mixin caret-start($width: $caret-width) {\n  border-top: $width solid transparent;\n  border-right: $width solid;\n  border-bottom: $width solid transparent;\n}\n\n@mixin caret(\n  $direction: down,\n  $width: $caret-width,\n  $spacing: $caret-spacing,\n  $vertical-align: $caret-vertical-align\n) {\n  @if $enable-caret {\n    &::after {\n      display: inline-block;\n      margin-left: $spacing;\n      vertical-align: $vertical-align;\n      content: \"\";\n      @if $direction == down {\n        @include caret-down($width);\n      } @else if $direction == up {\n        @include caret-up($width);\n      } @else if $direction == end {\n        @include caret-end($width);\n      }\n    }\n\n    @if $direction == start {\n      &::after {\n        display: none;\n      }\n\n      &::before {\n        display: inline-block;\n        margin-right: $spacing;\n        vertical-align: $vertical-align;\n        content: \"\";\n        @include caret-start($width);\n      }\n    }\n\n    &:empty::after {\n      margin-left: 0;\n    }\n  }\n}\n// scss-docs-end caret-mixins\n", "// Make the div behave like a button\n.btn-group,\n.btn-group-vertical {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle; // match .btn alignment given font-size hack above\n\n  > .btn {\n    position: relative;\n    flex: 1 1 auto;\n  }\n\n  // Bring the hover, focused, and \"active\" buttons to the front to overlay\n  // the borders properly\n  > .btn-check:checked + .btn,\n  > .btn-check:focus + .btn,\n  > .btn:hover,\n  > .btn:focus,\n  > .btn:active,\n  > .btn.active {\n    z-index: 1;\n  }\n}\n\n// Optional: Group multiple button groups together for a toolbar\n.btn-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n\n  .input-group {\n    width: auto;\n  }\n}\n\n.btn-group {\n  @include border-radius($btn-border-radius);\n\n  // Prevent double borders when buttons are next to each other\n  > :not(.btn-check:first-child) + .btn,\n  > .btn-group:not(:first-child) {\n    margin-left: calc(#{$btn-border-width} * -1); // stylelint-disable-line function-disallowed-list\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn.dropdown-toggle-split:first-child,\n  > .btn-group:not(:last-child) > .btn {\n    @include border-end-radius(0);\n  }\n\n  // The left radius should be 0 if the button is:\n  // - the \"third or more\" child\n  // - the second child and the previous element isn't `.btn-check` (making it the first child visually)\n  // - part of a btn-group which isn't the first child\n  > .btn:nth-child(n + 3),\n  > :not(.btn-check) + .btn,\n  > .btn-group:not(:first-child) > .btn {\n    @include border-start-radius(0);\n  }\n}\n\n// Sizing\n//\n// Remix the default button sizing classes into new ones for easier manipulation.\n\n.btn-group-sm > .btn { @extend .btn-sm; }\n.btn-group-lg > .btn { @extend .btn-lg; }\n\n\n//\n// Split button dropdowns\n//\n\n.dropdown-toggle-split {\n  padding-right: $btn-padding-x * .75;\n  padding-left: $btn-padding-x * .75;\n\n  &::after,\n  .dropup &::after,\n  .dropend &::after {\n    margin-left: 0;\n  }\n\n  .dropstart &::before {\n    margin-right: 0;\n  }\n}\n\n.btn-sm + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-sm * .75;\n  padding-left: $btn-padding-x-sm * .75;\n}\n\n.btn-lg + .dropdown-toggle-split {\n  padding-right: $btn-padding-x-lg * .75;\n  padding-left: $btn-padding-x-lg * .75;\n}\n\n\n// The clickable button for toggling the menu\n// Set the same inset shadow as the :active state\n.btn-group.show .dropdown-toggle {\n  @include box-shadow($btn-active-box-shadow);\n\n  // Show no shadow for `.btn-link` since it has no other button styles.\n  &.btn-link {\n    @include box-shadow(none);\n  }\n}\n\n\n//\n// Vertical button groups\n//\n\n.btn-group-vertical {\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n\n  > .btn,\n  > .btn-group {\n    width: 100%;\n  }\n\n  > .btn:not(:first-child),\n  > .btn-group:not(:first-child) {\n    margin-top: calc(#{$btn-border-width} * -1); // stylelint-disable-line function-disallowed-list\n  }\n\n  // Reset rounded corners\n  > .btn:not(:last-child):not(.dropdown-toggle),\n  > .btn-group:not(:last-child) > .btn {\n    @include border-bottom-radius(0);\n  }\n\n  > .btn ~ .btn,\n  > .btn-group:not(:first-child) > .btn {\n    @include border-top-radius(0);\n  }\n}\n", "// Base class\n//\n// Kickstart any navigation component with a set of style resets. Works with\n// `<nav>`s, `<ul>`s or `<ol>`s.\n\n.nav {\n  // scss-docs-start nav-css-vars\n  --#{$prefix}nav-link-padding-x: #{$nav-link-padding-x};\n  --#{$prefix}nav-link-padding-y: #{$nav-link-padding-y};\n  @include rfs($nav-link-font-size, --#{$prefix}nav-link-font-size);\n  --#{$prefix}nav-link-font-weight: #{$nav-link-font-weight};\n  --#{$prefix}nav-link-color: #{$nav-link-color};\n  --#{$prefix}nav-link-hover-color: #{$nav-link-hover-color};\n  --#{$prefix}nav-link-disabled-color: #{$nav-link-disabled-color};\n  // scss-docs-end nav-css-vars\n\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n\n.nav-link {\n  display: block;\n  padding: var(--#{$prefix}nav-link-padding-y) var(--#{$prefix}nav-link-padding-x);\n  @include font-size(var(--#{$prefix}nav-link-font-size));\n  font-weight: var(--#{$prefix}nav-link-font-weight);\n  color: var(--#{$prefix}nav-link-color);\n  text-decoration: if($link-decoration == none, null, none);\n  background: none;\n  border: 0;\n  @include transition($nav-link-transition);\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}nav-link-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n\n  &:focus-visible {\n    outline: 0;\n    box-shadow: $nav-link-focus-box-shadow;\n  }\n\n  // Disabled state lightens text\n  &.disabled {\n    color: var(--#{$prefix}nav-link-disabled-color);\n    pointer-events: none;\n    cursor: default;\n  }\n}\n\n//\n// Tabs\n//\n\n.nav-tabs {\n  // scss-docs-start nav-tabs-css-vars\n  --#{$prefix}nav-tabs-border-width: #{$nav-tabs-border-width};\n  --#{$prefix}nav-tabs-border-color: #{$nav-tabs-border-color};\n  --#{$prefix}nav-tabs-border-radius: #{$nav-tabs-border-radius};\n  --#{$prefix}nav-tabs-link-hover-border-color: #{$nav-tabs-link-hover-border-color};\n  --#{$prefix}nav-tabs-link-active-color: #{$nav-tabs-link-active-color};\n  --#{$prefix}nav-tabs-link-active-bg: #{$nav-tabs-link-active-bg};\n  --#{$prefix}nav-tabs-link-active-border-color: #{$nav-tabs-link-active-border-color};\n  // scss-docs-end nav-tabs-css-vars\n\n  border-bottom: var(--#{$prefix}nav-tabs-border-width) solid var(--#{$prefix}nav-tabs-border-color);\n\n  .nav-link {\n    margin-bottom: calc(-1 * var(--#{$prefix}nav-tabs-border-width)); // stylelint-disable-line function-disallowed-list\n    border: var(--#{$prefix}nav-tabs-border-width) solid transparent;\n    @include border-top-radius(var(--#{$prefix}nav-tabs-border-radius));\n\n    &:hover,\n    &:focus {\n      // Prevents active .nav-link tab overlapping focus outline of previous/next .nav-link\n      isolation: isolate;\n      border-color: var(--#{$prefix}nav-tabs-link-hover-border-color);\n    }\n\n    &.disabled,\n    &:disabled {\n      color: var(--#{$prefix}nav-link-disabled-color);\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .nav-item.show .nav-link {\n    color: var(--#{$prefix}nav-tabs-link-active-color);\n    background-color: var(--#{$prefix}nav-tabs-link-active-bg);\n    border-color: var(--#{$prefix}nav-tabs-link-active-border-color);\n  }\n\n  .dropdown-menu {\n    // Make dropdown border overlap tab border\n    margin-top: calc(-1 * var(--#{$prefix}nav-tabs-border-width)); // stylelint-disable-line function-disallowed-list\n    // Remove the top rounded corners here since there is a hard edge above the menu\n    @include border-top-radius(0);\n  }\n}\n\n\n//\n// Pills\n//\n\n.nav-pills {\n  // scss-docs-start nav-pills-css-vars\n  --#{$prefix}nav-pills-border-radius: #{$nav-pills-border-radius};\n  --#{$prefix}nav-pills-link-active-color: #{$nav-pills-link-active-color};\n  --#{$prefix}nav-pills-link-active-bg: #{$nav-pills-link-active-bg};\n  // scss-docs-end nav-pills-css-vars\n\n  .nav-link {\n    @include border-radius(var(--#{$prefix}nav-pills-border-radius));\n\n    &:disabled {\n      color: var(--#{$prefix}nav-link-disabled-color);\n      background-color: transparent;\n      border-color: transparent;\n    }\n  }\n\n  .nav-link.active,\n  .show > .nav-link {\n    color: var(--#{$prefix}nav-pills-link-active-color);\n    @include gradient-bg(var(--#{$prefix}nav-pills-link-active-bg));\n  }\n}\n\n\n//\n// Underline\n//\n\n.nav-underline {\n  // scss-docs-start nav-underline-css-vars\n  --#{$prefix}nav-underline-gap: #{$nav-underline-gap};\n  --#{$prefix}nav-underline-border-width: #{$nav-underline-border-width};\n  --#{$prefix}nav-underline-link-active-color: #{$nav-underline-link-active-color};\n  // scss-docs-end nav-underline-css-vars\n\n  gap: var(--#{$prefix}nav-underline-gap);\n\n  .nav-link {\n    padding-right: 0;\n    padding-left: 0;\n    border-bottom: var(--#{$prefix}nav-underline-border-width) solid transparent;\n\n    &:hover,\n    &:focus {\n      border-bottom-color: currentcolor;\n    }\n  }\n\n  .nav-link.active,\n  .show > .nav-link {\n    font-weight: $font-weight-bold;\n    color: var(--#{$prefix}nav-underline-link-active-color);\n    border-bottom-color: currentcolor;\n  }\n}\n\n\n//\n// Justified variants\n//\n\n.nav-fill {\n  > .nav-link,\n  .nav-item {\n    flex: 1 1 auto;\n    text-align: center;\n  }\n}\n\n.nav-justified {\n  > .nav-link,\n  .nav-item {\n    flex-basis: 0;\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n.nav-fill,\n.nav-justified {\n  .nav-item .nav-link {\n    width: 100%; // Make sure button will grow\n  }\n}\n\n\n// Tabbable tabs\n//\n// Hide tabbable panes to start, show them when `.active`\n\n.tab-content {\n  > .tab-pane {\n    display: none;\n  }\n  > .active {\n    display: block;\n  }\n}\n", "// Navbar\n//\n// Provide a static navbar from which we expand to create full-width, fixed, and\n// other navbar variations.\n\n.navbar {\n  // scss-docs-start navbar-css-vars\n  --#{$prefix}navbar-padding-x: #{if($navbar-padding-x == null, 0, $navbar-padding-x)};\n  --#{$prefix}navbar-padding-y: #{$navbar-padding-y};\n  --#{$prefix}navbar-color: #{$navbar-light-color};\n  --#{$prefix}navbar-hover-color: #{$navbar-light-hover-color};\n  --#{$prefix}navbar-disabled-color: #{$navbar-light-disabled-color};\n  --#{$prefix}navbar-active-color: #{$navbar-light-active-color};\n  --#{$prefix}navbar-brand-padding-y: #{$navbar-brand-padding-y};\n  --#{$prefix}navbar-brand-margin-end: #{$navbar-brand-margin-end};\n  --#{$prefix}navbar-brand-font-size: #{$navbar-brand-font-size};\n  --#{$prefix}navbar-brand-color: #{$navbar-light-brand-color};\n  --#{$prefix}navbar-brand-hover-color: #{$navbar-light-brand-hover-color};\n  --#{$prefix}navbar-nav-link-padding-x: #{$navbar-nav-link-padding-x};\n  --#{$prefix}navbar-toggler-padding-y: #{$navbar-toggler-padding-y};\n  --#{$prefix}navbar-toggler-padding-x: #{$navbar-toggler-padding-x};\n  --#{$prefix}navbar-toggler-font-size: #{$navbar-toggler-font-size};\n  --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-light-toggler-icon-bg)};\n  --#{$prefix}navbar-toggler-border-color: #{$navbar-light-toggler-border-color};\n  --#{$prefix}navbar-toggler-border-radius: #{$navbar-toggler-border-radius};\n  --#{$prefix}navbar-toggler-focus-width: #{$navbar-toggler-focus-width};\n  --#{$prefix}navbar-toggler-transition: #{$navbar-toggler-transition};\n  // scss-docs-end navbar-css-vars\n\n  position: relative;\n  display: flex;\n  flex-wrap: wrap; // allow us to do the line break for collapsing content\n  align-items: center;\n  justify-content: space-between; // space out brand from logo\n  padding: var(--#{$prefix}navbar-padding-y) var(--#{$prefix}navbar-padding-x);\n  @include gradient-bg();\n\n  // Because flex properties aren't inherited, we need to redeclare these first\n  // few properties so that content nested within behave properly.\n  // The `flex-wrap` property is inherited to simplify the expanded navbars\n  %container-flex-properties {\n    display: flex;\n    flex-wrap: inherit;\n    align-items: center;\n    justify-content: space-between;\n  }\n\n  > .container,\n  > .container-fluid {\n    @extend %container-flex-properties;\n  }\n\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    > .container#{breakpoint-infix($breakpoint, $container-max-widths)} {\n      @extend %container-flex-properties;\n    }\n  }\n}\n\n\n// Navbar brand\n//\n// Used for brand, project, or site names.\n\n.navbar-brand {\n  padding-top: var(--#{$prefix}navbar-brand-padding-y);\n  padding-bottom: var(--#{$prefix}navbar-brand-padding-y);\n  margin-right: var(--#{$prefix}navbar-brand-margin-end);\n  @include font-size(var(--#{$prefix}navbar-brand-font-size));\n  color: var(--#{$prefix}navbar-brand-color);\n  text-decoration: if($link-decoration == none, null, none);\n  white-space: nowrap;\n\n  &:hover,\n  &:focus {\n    color: var(--#{$prefix}navbar-brand-hover-color);\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n}\n\n\n// Navbar nav\n//\n// Custom navbar navigation (doesn't require `.nav`, but does make use of `.nav-link`).\n\n.navbar-nav {\n  // scss-docs-start navbar-nav-css-vars\n  --#{$prefix}nav-link-padding-x: 0;\n  --#{$prefix}nav-link-padding-y: #{$nav-link-padding-y};\n  @include rfs($nav-link-font-size, --#{$prefix}nav-link-font-size);\n  --#{$prefix}nav-link-font-weight: #{$nav-link-font-weight};\n  --#{$prefix}nav-link-color: var(--#{$prefix}navbar-color);\n  --#{$prefix}nav-link-hover-color: var(--#{$prefix}navbar-hover-color);\n  --#{$prefix}nav-link-disabled-color: var(--#{$prefix}navbar-disabled-color);\n  // scss-docs-end navbar-nav-css-vars\n\n  display: flex;\n  flex-direction: column; // cannot use `inherit` to get the `.navbar`s value\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n\n  .nav-link {\n    &.active,\n    &.show {\n      color: var(--#{$prefix}navbar-active-color);\n    }\n  }\n\n  .dropdown-menu {\n    position: static;\n  }\n}\n\n\n// Navbar text\n//\n//\n\n.navbar-text {\n  padding-top: $nav-link-padding-y;\n  padding-bottom: $nav-link-padding-y;\n  color: var(--#{$prefix}navbar-color);\n\n  a,\n  a:hover,\n  a:focus  {\n    color: var(--#{$prefix}navbar-active-color);\n  }\n}\n\n\n// Responsive navbar\n//\n// Custom styles for responsive collapsing and toggling of navbar contents.\n// Powered by the collapse Bootstrap JavaScript plugin.\n\n// When collapsed, prevent the toggleable navbar contents from appearing in\n// the default flexbox row orientation. Requires the use of `flex-wrap: wrap`\n// on the `.navbar` parent.\n.navbar-collapse {\n  flex-basis: 100%;\n  flex-grow: 1;\n  // For always expanded or extra full navbars, ensure content aligns itself\n  // properly vertically. Can be easily overridden with flex utilities.\n  align-items: center;\n}\n\n// Button for toggling the navbar when in its collapsed state\n.navbar-toggler {\n  padding: var(--#{$prefix}navbar-toggler-padding-y) var(--#{$prefix}navbar-toggler-padding-x);\n  @include font-size(var(--#{$prefix}navbar-toggler-font-size));\n  line-height: 1;\n  color: var(--#{$prefix}navbar-color);\n  background-color: transparent; // remove default button style\n  border: var(--#{$prefix}border-width) solid var(--#{$prefix}navbar-toggler-border-color); // remove default button style\n  @include border-radius(var(--#{$prefix}navbar-toggler-border-radius));\n  @include transition(var(--#{$prefix}navbar-toggler-transition));\n\n  &:hover {\n    text-decoration: none;\n  }\n\n  &:focus {\n    text-decoration: none;\n    outline: 0;\n    box-shadow: 0 0 0 var(--#{$prefix}navbar-toggler-focus-width);\n  }\n}\n\n// Keep as a separate element so folks can easily override it with another icon\n// or image file as needed.\n.navbar-toggler-icon {\n  display: inline-block;\n  width: 1.5em;\n  height: 1.5em;\n  vertical-align: middle;\n  background-image: var(--#{$prefix}navbar-toggler-icon-bg);\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: 100%;\n}\n\n.navbar-nav-scroll {\n  max-height: var(--#{$prefix}scroll-height, 75vh);\n  overflow-y: auto;\n}\n\n// scss-docs-start navbar-expand-loop\n// Generate series of `.navbar-expand-*` responsive classes for configuring\n// where your navbar collapses.\n.navbar-expand {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $next: breakpoint-next($breakpoint, $grid-breakpoints);\n    $infix: breakpoint-infix($next, $grid-breakpoints);\n\n    // stylelint-disable-next-line scss/selector-no-union-class-name\n    &#{$infix} {\n      @include media-breakpoint-up($next) {\n        flex-wrap: nowrap;\n        justify-content: flex-start;\n\n        .navbar-nav {\n          flex-direction: row;\n\n          .dropdown-menu {\n            position: absolute;\n          }\n\n          .nav-link {\n            padding-right: var(--#{$prefix}navbar-nav-link-padding-x);\n            padding-left: var(--#{$prefix}navbar-nav-link-padding-x);\n          }\n        }\n\n        .navbar-nav-scroll {\n          overflow: visible;\n        }\n\n        .navbar-collapse {\n          display: flex !important; // stylelint-disable-line declaration-no-important\n          flex-basis: auto;\n        }\n\n        .navbar-toggler {\n          display: none;\n        }\n\n        .offcanvas {\n          // stylelint-disable declaration-no-important\n          position: static;\n          z-index: auto;\n          flex-grow: 1;\n          width: auto !important;\n          height: auto !important;\n          visibility: visible !important;\n          background-color: transparent !important;\n          border: 0 !important;\n          transform: none !important;\n          @include box-shadow(none);\n          @include transition(none);\n          // stylelint-enable declaration-no-important\n\n          .offcanvas-header {\n            display: none;\n          }\n\n          .offcanvas-body {\n            display: flex;\n            flex-grow: 0;\n            padding: 0;\n            overflow-y: visible;\n          }\n        }\n      }\n    }\n  }\n}\n// scss-docs-end navbar-expand-loop\n\n// Navbar themes\n//\n// Styles for switching between navbars with light or dark background.\n\n.navbar-light {\n  @include deprecate(\"`.navbar-light`\", \"v5.2.0\", \"v6.0.0\", true);\n}\n\n.navbar-dark,\n.navbar[data-bs-theme=\"dark\"] {\n  // scss-docs-start navbar-dark-css-vars\n  --#{$prefix}navbar-color: #{$navbar-dark-color};\n  --#{$prefix}navbar-hover-color: #{$navbar-dark-hover-color};\n  --#{$prefix}navbar-disabled-color: #{$navbar-dark-disabled-color};\n  --#{$prefix}navbar-active-color: #{$navbar-dark-active-color};\n  --#{$prefix}navbar-brand-color: #{$navbar-dark-brand-color};\n  --#{$prefix}navbar-brand-hover-color: #{$navbar-dark-brand-hover-color};\n  --#{$prefix}navbar-toggler-border-color: #{$navbar-dark-toggler-border-color};\n  --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-dark-toggler-icon-bg)};\n  // scss-docs-end navbar-dark-css-vars\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .navbar-toggler-icon {\n      --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-dark-toggler-icon-bg)};\n    }\n  }\n}\n", "//\n// Base styles\n//\n\n.card {\n  // scss-docs-start card-css-vars\n  --#{$prefix}card-spacer-y: #{$card-spacer-y};\n  --#{$prefix}card-spacer-x: #{$card-spacer-x};\n  --#{$prefix}card-title-spacer-y: #{$card-title-spacer-y};\n  --#{$prefix}card-title-color: #{$card-title-color};\n  --#{$prefix}card-subtitle-color: #{$card-subtitle-color};\n  --#{$prefix}card-border-width: #{$card-border-width};\n  --#{$prefix}card-border-color: #{$card-border-color};\n  --#{$prefix}card-border-radius: #{$card-border-radius};\n  --#{$prefix}card-box-shadow: #{$card-box-shadow};\n  --#{$prefix}card-inner-border-radius: #{$card-inner-border-radius};\n  --#{$prefix}card-cap-padding-y: #{$card-cap-padding-y};\n  --#{$prefix}card-cap-padding-x: #{$card-cap-padding-x};\n  --#{$prefix}card-cap-bg: #{$card-cap-bg};\n  --#{$prefix}card-cap-color: #{$card-cap-color};\n  --#{$prefix}card-height: #{$card-height};\n  --#{$prefix}card-color: #{$card-color};\n  --#{$prefix}card-bg: #{$card-bg};\n  --#{$prefix}card-img-overlay-padding: #{$card-img-overlay-padding};\n  --#{$prefix}card-group-margin: #{$card-group-margin};\n  // scss-docs-end card-css-vars\n\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 0; // See https://github.com/twbs/bootstrap/pull/22740#issuecomment-305868106\n  height: var(--#{$prefix}card-height);\n  color: var(--#{$prefix}body-color);\n  word-wrap: break-word;\n  background-color: var(--#{$prefix}card-bg);\n  background-clip: border-box;\n  border: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n  @include border-radius(var(--#{$prefix}card-border-radius));\n  @include box-shadow(var(--#{$prefix}card-box-shadow));\n\n  > hr {\n    margin-right: 0;\n    margin-left: 0;\n  }\n\n  > .list-group {\n    border-top: inherit;\n    border-bottom: inherit;\n\n    &:first-child {\n      border-top-width: 0;\n      @include border-top-radius(var(--#{$prefix}card-inner-border-radius));\n    }\n\n    &:last-child  {\n      border-bottom-width: 0;\n      @include border-bottom-radius(var(--#{$prefix}card-inner-border-radius));\n    }\n  }\n\n  // Due to specificity of the above selector (`.card > .list-group`), we must\n  // use a child selector here to prevent double borders.\n  > .card-header + .list-group,\n  > .list-group + .card-footer {\n    border-top: 0;\n  }\n}\n\n.card-body {\n  // Enable `flex-grow: 1` for decks and groups so that card blocks take up\n  // as much space as possible, ensuring footers are aligned to the bottom.\n  flex: 1 1 auto;\n  padding: var(--#{$prefix}card-spacer-y) var(--#{$prefix}card-spacer-x);\n  color: var(--#{$prefix}card-color);\n}\n\n.card-title {\n  margin-bottom: var(--#{$prefix}card-title-spacer-y);\n  color: var(--#{$prefix}card-title-color);\n}\n\n.card-subtitle {\n  margin-top: calc(-.5 * var(--#{$prefix}card-title-spacer-y)); // stylelint-disable-line function-disallowed-list\n  margin-bottom: 0;\n  color: var(--#{$prefix}card-subtitle-color);\n}\n\n.card-text:last-child {\n  margin-bottom: 0;\n}\n\n.card-link {\n  &:hover {\n    text-decoration: if($link-hover-decoration == underline, none, null);\n  }\n\n  + .card-link {\n    margin-left: var(--#{$prefix}card-spacer-x);\n  }\n}\n\n//\n// Optional textual caps\n//\n\n.card-header {\n  padding: var(--#{$prefix}card-cap-padding-y) var(--#{$prefix}card-cap-padding-x);\n  margin-bottom: 0; // Removes the default margin-bottom of <hN>\n  color: var(--#{$prefix}card-cap-color);\n  background-color: var(--#{$prefix}card-cap-bg);\n  border-bottom: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n\n  &:first-child {\n    @include border-radius(var(--#{$prefix}card-inner-border-radius) var(--#{$prefix}card-inner-border-radius) 0 0);\n  }\n}\n\n.card-footer {\n  padding: var(--#{$prefix}card-cap-padding-y) var(--#{$prefix}card-cap-padding-x);\n  color: var(--#{$prefix}card-cap-color);\n  background-color: var(--#{$prefix}card-cap-bg);\n  border-top: var(--#{$prefix}card-border-width) solid var(--#{$prefix}card-border-color);\n\n  &:last-child {\n    @include border-radius(0 0 var(--#{$prefix}card-inner-border-radius) var(--#{$prefix}card-inner-border-radius));\n  }\n}\n\n\n//\n// Header navs\n//\n\n.card-header-tabs {\n  margin-right: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  margin-bottom: calc(-1 * var(--#{$prefix}card-cap-padding-y)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  border-bottom: 0;\n\n  .nav-link.active {\n    background-color: var(--#{$prefix}card-bg);\n    border-bottom-color: var(--#{$prefix}card-bg);\n  }\n}\n\n.card-header-pills {\n  margin-right: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}card-cap-padding-x)); // stylelint-disable-line function-disallowed-list\n}\n\n// Card image\n.card-img-overlay {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  padding: var(--#{$prefix}card-img-overlay-padding);\n  @include border-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n.card-img,\n.card-img-top,\n.card-img-bottom {\n  width: 100%; // Required because we use flexbox and this inherently applies align-self: stretch\n}\n\n.card-img,\n.card-img-top {\n  @include border-top-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n.card-img,\n.card-img-bottom {\n  @include border-bottom-radius(var(--#{$prefix}card-inner-border-radius));\n}\n\n\n//\n// Card groups\n//\n\n.card-group {\n  // The child selector allows nested `.card` within `.card-group`\n  // to display properly.\n  > .card {\n    margin-bottom: var(--#{$prefix}card-group-margin);\n  }\n\n  @include media-breakpoint-up(sm) {\n    display: flex;\n    flex-flow: row wrap;\n    // The child selector allows nested `.card` within `.card-group`\n    // to display properly.\n    > .card {\n      // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      flex: 1 0 0%;\n      margin-bottom: 0;\n\n      + .card {\n        margin-left: 0;\n        border-left: 0;\n      }\n\n      // Handle rounded corners\n      @if $enable-rounded {\n        &:not(:last-child) {\n          @include border-end-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-right-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-right-radius: 0;\n          }\n        }\n\n        &:not(:first-child) {\n          @include border-start-radius(0);\n\n          .card-img-top,\n          .card-header {\n            // stylelint-disable-next-line property-disallowed-list\n            border-top-left-radius: 0;\n          }\n          .card-img-bottom,\n          .card-footer {\n            // stylelint-disable-next-line property-disallowed-list\n            border-bottom-left-radius: 0;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Base class\n//\n// Requires one of the contextual, color modifier classes for `color` and\n// `background-color`.\n\n.badge {\n  // scss-docs-start badge-css-vars\n  --#{$prefix}badge-padding-x: #{$badge-padding-x};\n  --#{$prefix}badge-padding-y: #{$badge-padding-y};\n  @include rfs($badge-font-size, --#{$prefix}badge-font-size);\n  --#{$prefix}badge-font-weight: #{$badge-font-weight};\n  --#{$prefix}badge-color: #{$badge-color};\n  --#{$prefix}badge-border-radius: #{$badge-border-radius};\n  // scss-docs-end badge-css-vars\n\n  display: inline-block;\n  padding: var(--#{$prefix}badge-padding-y) var(--#{$prefix}badge-padding-x);\n  @include font-size(var(--#{$prefix}badge-font-size));\n  font-weight: var(--#{$prefix}badge-font-weight);\n  line-height: 1;\n  color: var(--#{$prefix}badge-color);\n  text-align: center;\n  white-space: nowrap;\n  vertical-align: baseline;\n  @include border-radius(var(--#{$prefix}badge-border-radius));\n  @include gradient-bg();\n\n  // Empty badges collapse automatically\n  &:empty {\n    display: none;\n  }\n}\n\n// Quick fix for badges in buttons\n.btn .badge {\n  position: relative;\n  top: -1px;\n}\n", "// Base class\n//\n// Easily usable on <ul>, <ol>, or <div>.\n\n.list-group {\n  // scss-docs-start list-group-css-vars\n  --#{$prefix}list-group-color: #{$list-group-color};\n  --#{$prefix}list-group-bg: #{$list-group-bg};\n  --#{$prefix}list-group-border-color: #{$list-group-border-color};\n  --#{$prefix}list-group-border-width: #{$list-group-border-width};\n  --#{$prefix}list-group-border-radius: #{$list-group-border-radius};\n  --#{$prefix}list-group-item-padding-x: #{$list-group-item-padding-x};\n  --#{$prefix}list-group-item-padding-y: #{$list-group-item-padding-y};\n  --#{$prefix}list-group-action-color: #{$list-group-action-color};\n  --#{$prefix}list-group-action-hover-color: #{$list-group-action-hover-color};\n  --#{$prefix}list-group-action-hover-bg: #{$list-group-hover-bg};\n  --#{$prefix}list-group-action-active-color: #{$list-group-action-active-color};\n  --#{$prefix}list-group-action-active-bg: #{$list-group-action-active-bg};\n  --#{$prefix}list-group-disabled-color: #{$list-group-disabled-color};\n  --#{$prefix}list-group-disabled-bg: #{$list-group-disabled-bg};\n  --#{$prefix}list-group-active-color: #{$list-group-active-color};\n  --#{$prefix}list-group-active-bg: #{$list-group-active-bg};\n  --#{$prefix}list-group-active-border-color: #{$list-group-active-border-color};\n  // scss-docs-end list-group-css-vars\n\n  display: flex;\n  flex-direction: column;\n\n  // No need to set list-style: none; since .list-group-item is block level\n  padding-left: 0; // reset padding because ul and ol\n  margin-bottom: 0;\n  @include border-radius(var(--#{$prefix}list-group-border-radius));\n}\n\n.list-group-numbered {\n  list-style-type: none;\n  counter-reset: section;\n\n  > .list-group-item::before {\n    // Increments only this instance of the section counter\n    content: counters(section, \".\") \". \";\n    counter-increment: section;\n  }\n}\n\n// Interactive list items\n//\n// Use anchor or button elements instead of `li`s or `div`s to create interactive\n// list items. Includes an extra `.active` modifier class for selected items.\n\n.list-group-item-action {\n  width: 100%; // For `<button>`s (anchors become 100% by default though)\n  color: var(--#{$prefix}list-group-action-color);\n  text-align: inherit; // For `<button>`s (anchors inherit)\n\n  // Hover state\n  &:hover,\n  &:focus {\n    z-index: 1; // Place hover/focus items above their siblings for proper border styling\n    color: var(--#{$prefix}list-group-action-hover-color);\n    text-decoration: none;\n    background-color: var(--#{$prefix}list-group-action-hover-bg);\n  }\n\n  &:active {\n    color: var(--#{$prefix}list-group-action-active-color);\n    background-color: var(--#{$prefix}list-group-action-active-bg);\n  }\n}\n\n// Individual list items\n//\n// Use on `li`s or `div`s within the `.list-group` parent.\n\n.list-group-item {\n  position: relative;\n  display: block;\n  padding: var(--#{$prefix}list-group-item-padding-y) var(--#{$prefix}list-group-item-padding-x);\n  color: var(--#{$prefix}list-group-color);\n  text-decoration: if($link-decoration == none, null, none);\n  background-color: var(--#{$prefix}list-group-bg);\n  border: var(--#{$prefix}list-group-border-width) solid var(--#{$prefix}list-group-border-color);\n\n  &:first-child {\n    @include border-top-radius(inherit);\n  }\n\n  &:last-child {\n    @include border-bottom-radius(inherit);\n  }\n\n  &.disabled,\n  &:disabled {\n    color: var(--#{$prefix}list-group-disabled-color);\n    pointer-events: none;\n    background-color: var(--#{$prefix}list-group-disabled-bg);\n  }\n\n  // Include both here for `<a>`s and `<button>`s\n  &.active {\n    z-index: 2; // Place active items above their siblings for proper border styling\n    color: var(--#{$prefix}list-group-active-color);\n    background-color: var(--#{$prefix}list-group-active-bg);\n    border-color: var(--#{$prefix}list-group-active-border-color);\n  }\n\n  // stylelint-disable-next-line scss/selector-no-redundant-nesting-selector\n  & + .list-group-item {\n    border-top-width: 0;\n\n    &.active {\n      margin-top: calc(-1 * var(--#{$prefix}list-group-border-width)); // stylelint-disable-line function-disallowed-list\n      border-top-width: var(--#{$prefix}list-group-border-width);\n    }\n  }\n}\n\n// Horizontal\n//\n// Change the layout of list group items from vertical (default) to horizontal.\n\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .list-group-horizontal#{$infix} {\n      flex-direction: row;\n\n      > .list-group-item {\n        &:first-child:not(:last-child) {\n          @include border-bottom-start-radius(var(--#{$prefix}list-group-border-radius));\n          @include border-top-end-radius(0);\n        }\n\n        &:last-child:not(:first-child) {\n          @include border-top-end-radius(var(--#{$prefix}list-group-border-radius));\n          @include border-bottom-start-radius(0);\n        }\n\n        &.active {\n          margin-top: 0;\n        }\n\n        + .list-group-item {\n          border-top-width: var(--#{$prefix}list-group-border-width);\n          border-left-width: 0;\n\n          &.active {\n            margin-left: calc(-1 * var(--#{$prefix}list-group-border-width)); // stylelint-disable-line function-disallowed-list\n            border-left-width: var(--#{$prefix}list-group-border-width);\n          }\n        }\n      }\n    }\n  }\n}\n\n\n// Flush list items\n//\n// Remove borders and border-radius to keep list group items edge-to-edge. Most\n// useful within other components (e.g., cards).\n\n.list-group-flush {\n  @include border-radius(0);\n\n  > .list-group-item {\n    border-width: 0 0 var(--#{$prefix}list-group-border-width);\n\n    &:last-child {\n      border-bottom-width: 0;\n    }\n  }\n}\n\n\n// scss-docs-start list-group-modifiers\n// List group contextual variants\n//\n// Add modifier classes to change text and background color on individual items.\n// Organizationally, this must come after the `:hover` states.\n\n@each $state in map-keys($theme-colors) {\n  .list-group-item-#{$state} {\n    --#{$prefix}list-group-color: var(--#{$prefix}#{$state}-text-emphasis);\n    --#{$prefix}list-group-bg: var(--#{$prefix}#{$state}-bg-subtle);\n    --#{$prefix}list-group-border-color: var(--#{$prefix}#{$state}-border-subtle);\n    --#{$prefix}list-group-action-hover-color: var(--#{$prefix}emphasis-color);\n    --#{$prefix}list-group-action-hover-bg: var(--#{$prefix}#{$state}-border-subtle);\n    --#{$prefix}list-group-action-active-color: var(--#{$prefix}emphasis-color);\n    --#{$prefix}list-group-action-active-bg: var(--#{$prefix}#{$state}-border-subtle);\n    --#{$prefix}list-group-active-color: var(--#{$prefix}#{$state}-bg-subtle);\n    --#{$prefix}list-group-active-bg: var(--#{$prefix}#{$state}-text-emphasis);\n    --#{$prefix}list-group-active-border-color: var(--#{$prefix}#{$state}-text-emphasis);\n  }\n}\n// scss-docs-end list-group-modifiers\n", "// Transparent background and border properties included for button version.\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n.btn-close {\n  // scss-docs-start close-css-vars\n  --#{$prefix}btn-close-color: #{$btn-close-color};\n  --#{$prefix}btn-close-bg: #{ escape-svg($btn-close-bg) };\n  --#{$prefix}btn-close-opacity: #{$btn-close-opacity};\n  --#{$prefix}btn-close-hover-opacity: #{$btn-close-hover-opacity};\n  --#{$prefix}btn-close-focus-shadow: #{$btn-close-focus-shadow};\n  --#{$prefix}btn-close-focus-opacity: #{$btn-close-focus-opacity};\n  --#{$prefix}btn-close-disabled-opacity: #{$btn-close-disabled-opacity};\n  --#{$prefix}btn-close-white-filter: #{$btn-close-white-filter};\n  // scss-docs-end close-css-vars\n\n  box-sizing: content-box;\n  width: $btn-close-width;\n  height: $btn-close-height;\n  padding: $btn-close-padding-y $btn-close-padding-x;\n  color: var(--#{$prefix}btn-close-color);\n  background: transparent var(--#{$prefix}btn-close-bg) center / $btn-close-width auto no-repeat; // include transparent for button elements\n  border: 0; // for button elements\n  @include border-radius();\n  opacity: var(--#{$prefix}btn-close-opacity);\n\n  // Override <a>'s hover style\n  &:hover {\n    color: var(--#{$prefix}btn-close-color);\n    text-decoration: none;\n    opacity: var(--#{$prefix}btn-close-hover-opacity);\n  }\n\n  &:focus {\n    outline: 0;\n    box-shadow: var(--#{$prefix}btn-close-focus-shadow);\n    opacity: var(--#{$prefix}btn-close-focus-opacity);\n  }\n\n  &:disabled,\n  &.disabled {\n    pointer-events: none;\n    user-select: none;\n    opacity: var(--#{$prefix}btn-close-disabled-opacity);\n  }\n}\n\n@mixin btn-close-white() {\n  filter: var(--#{$prefix}btn-close-white-filter);\n}\n\n.btn-close-white {\n  @include btn-close-white();\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    .btn-close {\n      @include btn-close-white();\n    }\n  }\n}\n", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\n@each $color, $value in $theme-colors {\n  $color-rgb: to-rgb($value);\n  .text-bg-#{$color} {\n    color: color-contrast($value) if($enable-important-utilities, !important, null);\n    background-color: RGBA($color-rgb, var(--#{$prefix}bg-opacity, 1)) if($enable-important-utilities, !important, null);\n  }\n}\n", "// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\n@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n    text-decoration-color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n\n    @if $link-shade-percentage != 0 {\n      &:hover,\n      &:focus {\n        $hover-color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\n        color: RGBA(#{to-rgb($hover-color)}, var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n        text-decoration-color: RGBA(to-rgb($hover-color), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n      }\n    }\n  }\n}\n\n// One-off special link helper as a bridge until v6\n.link-body-emphasis {\n  color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n  text-decoration-color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n\n  @if $link-shade-percentage != 0 {\n    &:hover,\n    &:focus {\n      color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-opacity, .75)) if($enable-important-utilities, !important, null);\n      text-decoration-color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-underline-opacity, .75)) if($enable-important-utilities, !important, null);\n    }\n  }\n}\n", ".focus-ring:focus {\n  outline: 0;\n  // By default, there is no `--bs-focus-ring-x`, `--bs-focus-ring-y`, or `--bs-focus-ring-blur`, but we provide CSS variables with fallbacks to initial `0` values\n  box-shadow: var(--#{$prefix}focus-ring-x, 0) var(--#{$prefix}focus-ring-y, 0) var(--#{$prefix}focus-ring-blur, 0) var(--#{$prefix}focus-ring-width) var(--#{$prefix}focus-ring-color);\n}\n", ".icon-link {\n  display: inline-flex;\n  gap: $icon-link-gap;\n  align-items: center;\n  text-decoration-color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, .5));\n  text-underline-offset: $icon-link-underline-offset;\n  backface-visibility: hidden;\n\n  > .bi {\n    flex-shrink: 0;\n    width: $icon-link-icon-size;\n    height: $icon-link-icon-size;\n    fill: currentcolor;\n    @include transition($icon-link-icon-transition);\n  }\n}\n\n.icon-link-hover {\n  &:hover,\n  &:focus-visible {\n    > .bi {\n      transform: var(--#{$prefix}icon-link-transform, $icon-link-icon-transform);\n    }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top and bottom\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n\n    .sticky#{$infix}-bottom {\n      position: sticky;\n      bottom: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "// scss-docs-start stacks\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n// scss-docs-end stacks\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://kittygiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n\n  // Fix for positioned table caption that could become anonymous cells\n  &:not(caption) {\n    position: absolute !important;\n  }\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/TR/2013/NOTE-WCAG20-TECHS-20130905/G1\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "//\n// Text truncation\n//\n\n.text-truncate {\n  @include text-truncate();\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", ".vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 1px;\n  min-height: 1em;\n  background-color: currentcolor;\n  opacity: $hr-opacity;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix: \"\", $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // Use custom CSS variable name if present, otherwise default to `class`\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (e.g. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$prefix}#{$css-variable-name}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$prefix}#{$css-variable-name}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $variable in $is-local-vars {\n                --#{$prefix}#{$local-var}: #{$variable};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              @if $is-local-vars {\n                @each $local-var, $variable in $is-local-vars {\n                  --#{$prefix}#{$local-var}: #{$variable};\n                }\n              }\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n", ".avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n\r\n.avatar-lg {\r\n  width: 64px;\r\n  height: 64px;\r\n}\r\n\r\n.avatar-title {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: $primary;\r\n}", ".btn .feather {\r\n  width: 14px;\r\n  height: 14px;\r\n}\r\n\r\n@each $color, $value in $theme-colors {\r\n  .btn-#{$color} {\r\n    @include button-color($white);\r\n    @include button-color-hover($white);\r\n  }\r\n}\r\n\r\n@each $color, $value in $social-colors {\r\n  .btn-#{$color} {\r\n    @include button-variant($value, $value);\r\n    @include button-color($white);\r\n    @include button-color-hover($white);\r\n  }\r\n}\r\n\r\n.btn-light,\r\n.btn-white {\r\n  @include button-color($gray-800);\r\n  @include button-color-hover($gray-800);\r\n}", "@mixin button-color($color) {\r\n  &,\r\n  &:focus,\r\n  &.focus,\r\n  &.disabled,\r\n  &:disabled,\r\n  .show > &.dropdown-toggle {\r\n    color: $color;\r\n  }\r\n}\r\n\r\n@mixin button-color-hover($color) {\r\n  &:hover:not(:disabled):not(.disabled),\r\n  &.hover:not(:disabled):not(.disabled) {\r\n    color: $color;\r\n  }\r\n}\r\n\r\n@mixin button-color-active($color) {\r\n  &:not(:disabled):not(.disabled):active,\r\n  &:not(:disabled):not(.disabled).active {\r\n    color: $color;\r\n  }\r\n}\r\n", ".card {\r\n  margin-bottom: $grid-gutter-width;\r\n  box-shadow: $card-shadow;\r\n}\r\n\r\n.card-header {\r\n  border-bottom-width: 1px;\r\n}\r\n\r\n.card-title {\r\n  font-size: $card-title-font-size;\r\n  font-weight: $card-title-font-weight;\r\n  color: $card-title-color;\r\n}\r\n\r\n.card-subtitle {\r\n  font-weight: $font-weight-normal;\r\n}\r\n\r\n.card-img,\r\n.card-img-top,\r\n.card-img-bottom {\r\n  @include img-fluid;\r\n\r\n  @media all and (-ms-high-contrast:none) {\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.card > .table > tbody {\r\n  tr:last-child td:first-child,\r\n  tr:last-child th:first-child {\r\n    border-bottom-left-radius: $card-border-radius;\r\n  }\r\n  tr:last-child td:last-child,\r\n  tr:last-child th:last-child {\r\n    border-bottom-right-radius: $card-border-radius;\r\n  }\r\n}", ".chart {\r\n  margin: auto;\r\n  position: relative;\r\n  width: 100%;\r\n  min-height: 300px;\r\n\r\n  &-xs {\r\n    min-height: 200px;\r\n  }\r\n\r\n  &-sm {\r\n    min-height: 252px;\r\n  }\r\n\r\n  &-lg {\r\n    min-height: 350px;\r\n  }\r\n\r\n  &-xl {\r\n    min-height: 500px;\r\n  }\r\n\r\n  canvas {\r\n    max-width: 100%;\r\n  }\r\n}\r\n", ".content {\r\n  padding: $content-padding-mobile;\r\n  flex: 1;\r\n  width: 100vw;\r\n  max-width: 100vw;\r\n  direction: ltr;\r\n\r\n  @include media-breakpoint-up(md) {\r\n    width: auto;\r\n    max-width: auto;\r\n  }\r\n\r\n  @include media-breakpoint-up(lg) {\r\n    padding: $content-padding-desktop;  \r\n  }\r\n}\r\n", ".navbar-nav .dropdown-menu {\r\n  box-shadow: $box-shadow;\r\n}\r\n\r\n.dropdown .dropdown-menu.show {\r\n  animation-name: dropdownAnimation;\r\n  animation-duration: .25s;\r\n  animation-iteration-count: 1;\r\n  animation-timing-function: ease;\r\n  animation-fill-mode: forwards;\r\n}\r\n\r\n@keyframes dropdownAnimation {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-8px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translate(0);\r\n  }\r\n}\r\n\r\n.dropdown-toggle:after {\r\n  border: solid;\r\n  border-width: 0 2px 2px 0;\r\n  display: inline-block;\r\n  padding: 2px;\r\n  transform: rotate(45deg);\r\n}\r\n\r\n.dropdown-item {\r\n  transition: $transition-appearance-fast;\r\n}\r\n\r\n.dropdown-menu {\r\n  top: auto;\r\n}\r\n\r\n.dropdown-menu-lg {\r\n  min-width: $dropdown-min-width*2;\r\n}\r\n\r\n.dropdown .list-group .list-group-item {\r\n  border-width: 0;\r\n  border-bottom-width: 1px;\r\n  margin-bottom: 0;\r\n\r\n  &:first-child,\r\n  &:last-child {\r\n    border-radius: 0;\r\n  }\r\n\r\n  &:hover {\r\n    background: $gray-100;\r\n  }\r\n}\r\n\r\n.dropdown-menu-header {\r\n  padding: $spacer*0.75;\r\n  text-align: center;\r\n  font-weight: $font-weight-bold;\r\n  border-bottom: 1px solid $gray-300;\r\n}\r\n\r\n.dropdown-menu-footer {\r\n  padding: $spacer*0.5;\r\n  text-align: center;\r\n  display: block;\r\n  font-size: $font-size-sm;\r\n}\r\n", ".feather {\r\n  width: 18px;\r\n  height: 18px;\r\n  stroke-width: 2;\r\n}\r\n\r\n.feather-sm {\r\n  width: 14px;\r\n  height: 14px;\r\n}\r\n\r\n.feather-lg {\r\n  width: 36px;\r\n  height: 36px;\r\n}\r\n", "footer.footer {\r\n  padding: $spacer $spacer*0.875;\r\n  direction: ltr;\r\n  background: $footer-bg;\r\n\r\n  ul {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  @include media-breakpoint-down(md) {\r\n    width: 100vw;\r\n  }\r\n}\r\n", ".hamburger,\r\n.hamburger:before,\r\n.hamburger:after {\r\n  cursor: pointer;\r\n  border-radius: 1px;\r\n  height: 3px;\r\n  width: $hamburger-width-middle;\r\n  background: $gray-700;\r\n  display: block;\r\n  content: '';\r\n  transition: $transition-appearance-fast;\r\n}\r\n\r\n.hamburger {\r\n  position: relative;\r\n}\r\n\r\n.hamburger:before {\r\n  top: -7.5px;\r\n  width: $hamburger-width-top;\r\n  position: absolute;\r\n}\r\n\r\n.hamburger:after {\r\n  bottom: -7.5px;\r\n  width: $hamburger-width-bottom;\r\n  position: absolute;\r\n}\r\n\r\n.sidebar-toggle:hover {\r\n  .hamburger,\r\n  .hamburger:before,\r\n  .hamburger:after {\r\n    background: $primary;\r\n  }\r\n}\r\n\r\n.hamburger-right {\r\n  &,\r\n  &:before,\r\n  &:after {\r\n    right: 0;\r\n  }\r\n}\r\n", "a.list-group-item {\r\n  text-decoration: none;\r\n}", ".main {\r\n  display: flex;\r\n  width: 100%;\r\n  min-width: 0;\r\n  min-height: 100vh;\r\n  transition: $sidebar-transition;\r\n  background: $body-bg;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  border-top-left-radius: 0;\r\n  border-bottom-left-radius: 0;\r\n}\r\n", ".navbar {\r\n  border-bottom: $navbar-border-bottom;\r\n  box-shadow: $navbar-box-shadow;\r\n\r\n  @include media-breakpoint-down(md) {\r\n    width: 100vw;\r\n  }\r\n}\r\n\r\n.navbar .avatar {\r\n  margin-top: -15px;\r\n  margin-bottom: -15px;\r\n}\r\n\r\n.navbar-nav {\r\n  direction: ltr;\r\n}\r\n\r\n.navbar-align {\r\n  margin-left: auto;\r\n}\r\n\r\n.navbar-bg {\r\n  background: $navbar-bg;\r\n}\r\n\r\n.navbar-brand {\r\n  font-weight: $navbar-brand-font-weight;\r\n  font-size: $navbar-brand-font-size;\r\n  padding: $navbar-brand-padding-y $navbar-brand-padding-x;\r\n  color: $navbar-brand-color;\r\n  display: block;\r\n\r\n  svg,\r\n  .feather {\r\n    color: $navbar-brand-icon-color;\r\n    height: 24px;\r\n    width: 24px;\r\n    margin-left: -0.15rem;\r\n    margin-right: 0.375rem;\r\n    margin-top: -0.375rem;\r\n  }\r\n}\r\n\r\n.nav-icon,\r\n.nav-flag {\r\n  padding: .1rem .8rem;\r\n  display: block;\r\n  font-size: 1.5rem;\r\n  color: $gray-600;\r\n  transition: $transition-appearance-fast;\r\n  line-height: 1.4;\r\n\r\n  &:after {\r\n    display: none !important;\r\n  }\r\n\r\n  &:hover,\r\n  &.active {\r\n    color: $primary;\r\n  }\r\n\r\n  svg,\r\n  .feather {\r\n    width: 20px;\r\n    height: 20px;\r\n  }\r\n}\r\n\r\n.nav-item {\r\n  .indicator {\r\n    background: $primary;\r\n    box-shadow: $box-shadow;\r\n    border-radius: 50%;\r\n    display: block;\r\n    height: 18px;\r\n    width: 18px;\r\n    padding: 1px;\r\n    position: absolute;\r\n    top: 0;\r\n    right: -8px;\r\n    text-align: center;\r\n    transition: top .1s ease-out;\r\n    font-size: 0.675rem;\r\n    color: $white;\r\n  }\r\n\r\n  &:hover .indicator {\r\n    top: -4px;\r\n  }\r\n\r\n  a:focus {\r\n    outline: 0;\r\n  }\r\n}\r\n\r\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\r\n  .navbar .avatar {\r\n    max-height: 47px;\r\n  }\r\n}\r\n\r\n@include media-breakpoint-down(sm) {\r\n  .navbar {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .nav-icon {\r\n    padding: .1rem .75rem;\r\n  }\r\n\r\n  .dropdown,\r\n  .dropleft,\r\n  .dropright,\r\n  .dropup {\r\n    position: inherit;\r\n  }\r\n\r\n  .navbar-expand .navbar-nav .dropdown-menu-lg {\r\n    min-width: 100%;\r\n  }\r\n\r\n  .nav-item .nav-link:after {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.nav-flag img {\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  object-fit: cover;\r\n}\r\n\r\n.navbar input {\r\n  direction: ltr;\r\n}", "body, html, #root {\r\n  height: 100%;\r\n}\r\n\r\nbody {\r\n  overflow-y: scroll;\r\n  opacity: 1 !important;\r\n}\r\n\r\n@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {\r\n  html {\r\n    overflow-x: hidden;\r\n  }\r\n}\r\n", ".sidebar {\r\n  min-width: $sidebar-width;\r\n  max-width: $sidebar-width;\r\n  transition: $sidebar-transition;\r\n  direction: ltr;\r\n  background: $sidebar-bg;\r\n}\r\n\r\n.sidebar-content {\r\n  transition: $sidebar-transition;\r\n  display: flex;\r\n  height: 100vh;\r\n  flex-direction: column;\r\n  background: $sidebar-bg;\r\n}\r\n\r\n.sidebar-nav {\r\n  padding-left: 0;\r\n  margin-bottom: 0;\r\n  list-style: none;\r\n  flex-grow: 1;\r\n}\r\n\r\n// Sidebar links\r\n.sidebar-link,\r\na.sidebar-link {\r\n  display: block;\r\n  padding: $sidebar-link-padding;\r\n  font-weight: $sidebar-link-font-weight;\r\n  transition: background .1s ease-in-out;\r\n  position: relative;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  border-left-style: solid;\r\n  border-left-width: 3px;\r\n  color: $sidebar-link-color;\r\n  background: $sidebar-link-bg;\r\n  border-left-color: $sidebar-link-border-left-color;\r\n\r\n  i,\r\n  svg {\r\n    margin-right: .75rem;\r\n    color: $sidebar-link-icon-color;\r\n  }\r\n}\r\n\r\n.sidebar-link:focus {\r\n  outline: 0;\r\n}\r\n\r\n.sidebar-link:hover {\r\n  color: $sidebar-link-hover-color;\r\n  background: $sidebar-link-hover-bg;\r\n  border-left-color: $sidebar-link-hover-border-left-color;\r\n\r\n  i,\r\n  svg {\r\n    color: $sidebar-link-icon-hover-color;\r\n  }\r\n}\r\n\r\n.sidebar-item.active > .sidebar-link,\r\n.sidebar-item.active .sidebar-link:hover {\r\n  color: $sidebar-link-active-color;\r\n  background: $sidebar-link-active-bg;\r\n  border-left-color: $sidebar-link-active-border-left-color;\r\n\r\n  i,\r\n  svg {\r\n    color: $sidebar-link-icon-active-color;\r\n  }\r\n}\r\n\r\n// Sidebar brand\r\n.sidebar-brand {\r\n  font-weight: $sidebar-brand-font-weight;\r\n  font-size: $sidebar-brand-font-size;\r\n  padding: $sidebar-brand-padding-y $sidebar-brand-padding-x;\r\n  display: block;\r\n  color: $sidebar-brand-color;\r\n\r\n  &:hover {\r\n    text-decoration: none;\r\n    color: $sidebar-brand-color;\r\n  }\r\n\r\n  &:focus {\r\n    outline: 0;\r\n  }\r\n}\r\n\r\n// Toggle states\r\n.sidebar-toggle {\r\n  cursor: pointer;\r\n  width: 26px;\r\n  height: 26px;\r\n  display: flex;\r\n}\r\n\r\n.sidebar {\r\n  &.collapsed {\r\n    margin-left: -$sidebar-width;\r\n  }\r\n\r\n  // Workaround for IE bug, more info:\r\n  // https://stackoverflow.com/a/25850649\r\n  @media (min-width: 1px) and (max-width: #{map-get($grid-breakpoints, 'lg') - .02px}) {\r\n\r\n    // Sidebar default state (on mobile)\r\n    margin-left: -$sidebar-width;\r\n\r\n    // Sidebar collapsed state (on mobile)\r\n    &.collapsed {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.sidebar-toggle {\r\n  margin-right: $spacer;\r\n}\r\n\r\n// Sidebar header\r\n.sidebar-header {\r\n  background: transparent;\r\n  padding: $sidebar-header-padding;\r\n  font-size: $sidebar-header-font-size;\r\n  color: $sidebar-header-color;\r\n}\r\n\r\n// Badge\r\n.sidebar-badge {\r\n  position: absolute;\r\n  right: 15px;\r\n  top: 14px;\r\n  z-index: 1;\r\n}\r\n\r\n// Sidebar bottom\r\n.sidebar-cta-content {\r\n  padding: $sidebar-cta-padding;\r\n  margin: $sidebar-cta-margin;\r\n  border-radius: $sidebar-cta-border-radius;\r\n\r\n  background: $sidebar-cta-bg;\r\n  color: $sidebar-cta-color;\r\n}", ".min-vw-50 { min-width: 50vw !important; }\r\n.min-vh-50 { min-height: 50vh !important; }\r\n\r\n.vw-50 { width: 50vw !important; }\r\n.vh-50 { height: 50vh !important; }", ".table {\r\n\tthead, tbody, tfoot, tr, td, th {\r\n\t\tborder-color: $table-border-color;\r\n\t}\r\n\t\r\n\t> :not(:last-child) > :last-child > * {\r\n\t\tborder-color: $table-border-color;\r\n\t}\r\n}\r\n\r\n.table > tbody > tr > td {\r\n\tvertical-align: middle;\r\n}", ".stat {\r\n  background: $stat-bg;\r\n  border-radius: 50%;\r\n  width: 40px;\r\n  height: 40px;\r\n  align-items: center;\r\n  justify-content: center;\r\n  display: flex;\r\n\r\n  svg {\r\n    width: 18px;\r\n    height: 18px;\r\n    color: $stat-icon-color !important;\r\n    stroke-width: 1.5;\r\n  }\r\n}", ".text-sm {\r\n  font-size: $font-size-sm;\r\n}\r\n\r\n.text-lg {\r\n  font-size: $font-size-lg;\r\n}\r\n\r\nb, strong {\r\n\tfont-weight: $font-weight-bold;\r\n}\r\n\r\npre.snippet {\r\n  white-space: pre-wrap;\r\n  word-wrap: break-word;\r\n  text-align: justify;\r\n}\r\n\r\na {\r\n  cursor: pointer;\r\n}", ".wrapper {\r\n  align-items: stretch;\r\n  display: flex;\r\n  width: 100%;\r\n  background: $sidebar-bg;\r\n}\r\n", ".cursor-grab {\r\n  cursor: move;\r\n  cursor: grab;\r\n  cursor: -moz-grab;\r\n  cursor: -webkit-grab;\r\n}\r\n\r\n.cursor-pointer {\r\n  cursor: pointer;\r\n}", "svg {\n  -ms-touch-action: none;\n      touch-action: none;\n}\n\nimage, text, .jvm-zoomin, .jvm-zoomout {\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n}\n\n.jvm-container {\n  -ms-touch-action: none;\n      touch-action: none;\n  position: relative;\n  overflow: hidden;\n  height: 100%;\n  width: 100%;\n}\n\n.jvm-tooltip {\n  border-radius: 3px;\n  background-color: #5c5cff;\n  font-family: sans-serif, Verdana;\n  font-size: smaller;\n  box-shadow: 1px 2px 12px rgba(0, 0, 0, 0.2);\n  padding: 3px 5px;\n  white-space: nowrap;\n  position: absolute;\n  display: none;\n  color: #FFF;\n}\n\n.jvm-tooltip.active {\n  display: block;\n}\n\n.jvm-zoom-btn {\n  border-radius: 3px;\n  background-color: #292929;\n  padding: 3px;\n  box-sizing: border-box;\n  position: absolute;\n  line-height: 10px;\n  cursor: pointer;\n  color: #FFF;\n  height: 15px;\n  width: 15px;\n  left: 10px;\n}\n\n.jvm-zoom-btn.jvm-zoomout {\n  top: 30px;\n}\n\n.jvm-zoom-btn.jvm-zoomin {\n  top: 10px;\n}\n\n.jvm-series-container {\n  right: 15px;\n  position: absolute;\n}\n\n.jvm-series-container.jvm-series-h {\n  bottom: 15px;\n}\n\n.jvm-series-container.jvm-series-v {\n  top: 15px;\n}\n\n.jvm-series-container .jvm-legend {\n  background-color: #FFF;\n  border: 1px solid #e5e7eb;\n  margin-left: 0.75rem;\n  border-radius: 0.25rem;\n  border-color: #e5e7eb;\n  padding: 0.6rem;\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  float: left;\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-title {\n  line-height: 1;\n  border-bottom: 1px solid #e5e7eb;\n  padding-bottom: 0.5rem;\n  margin-bottom: 0.575rem;\n  text-align: left;\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-inner {\n  overflow: hidden;\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick {\n  overflow: hidden;\n  min-width: 40px;\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick:not(:first-child) {\n  margin-top: 0.575rem;\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-sample {\n  border-radius: 4px;\n  margin-right: .65rem;\n  height: 16px;\n  width: 16px;\n  float: left;\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-text {\n  font-size: 12px;\n  text-align: center;\n  float: left;\n}\n\n.jvm-line[animation=\"true\"] {\n  -webkit-animation: jvm-line-animation 10s linear forwards infinite;\n  animation: jvm-line-animation 10s linear forwards infinite;\n}\n\n@-webkit-keyframes jvm-line-animation {\n  from {\n    stroke-dashoffset: 250;\n  }\n}\n\n@keyframes jvm-line-animation {\n  from {\n    stroke-dashoffset: 250;\n  }\n}\n", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  left: 0;\n  right: 0;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: black;\n  border-radius: 7px;\n  left: 2px;\n  right: 2px;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n}\n", ".flatpickr-calendar {\n  background: transparent;\n  opacity: 0;\n  display: none;\n  text-align: center;\n  visibility: hidden;\n  padding: 0;\n  -webkit-animation: none;\n          animation: none;\n  direction: ltr;\n  border: 0;\n  font-size: 14px;\n  line-height: 24px;\n  border-radius: 5px;\n  position: absolute;\n  width: 307.875px;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  -ms-touch-action: manipulation;\n      touch-action: manipulation;\n  background: #fff;\n  -webkit-box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0,0,0,0.08);\n          box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0,0,0,0.08);\n}\n.flatpickr-calendar.open,\n.flatpickr-calendar.inline {\n  opacity: 1;\n  max-height: 640px;\n  visibility: visible;\n}\n.flatpickr-calendar.open {\n  display: inline-block;\n  z-index: 99999;\n}\n.flatpickr-calendar.animate.open {\n  -webkit-animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);\n          animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);\n}\n.flatpickr-calendar.inline {\n  display: block;\n  position: relative;\n  top: 2px;\n}\n.flatpickr-calendar.static {\n  position: absolute;\n  top: calc(100% + 2px);\n}\n.flatpickr-calendar.static.open {\n  z-index: 999;\n  display: block;\n}\n.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7) {\n  -webkit-box-shadow: none !important;\n          box-shadow: none !important;\n}\n.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1) {\n  -webkit-box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;\n          box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;\n}\n.flatpickr-calendar .hasWeeks .dayContainer,\n.flatpickr-calendar .hasTime .dayContainer {\n  border-bottom: 0;\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.flatpickr-calendar .hasWeeks .dayContainer {\n  border-left: 0;\n}\n.flatpickr-calendar.hasTime .flatpickr-time {\n  height: 40px;\n  border-top: 1px solid #e6e6e6;\n}\n.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {\n  height: auto;\n}\n.flatpickr-calendar:before,\n.flatpickr-calendar:after {\n  position: absolute;\n  display: block;\n  pointer-events: none;\n  border: solid transparent;\n  content: '';\n  height: 0;\n  width: 0;\n  left: 22px;\n}\n.flatpickr-calendar.rightMost:before,\n.flatpickr-calendar.arrowRight:before,\n.flatpickr-calendar.rightMost:after,\n.flatpickr-calendar.arrowRight:after {\n  left: auto;\n  right: 22px;\n}\n.flatpickr-calendar.arrowCenter:before,\n.flatpickr-calendar.arrowCenter:after {\n  left: 50%;\n  right: 50%;\n}\n.flatpickr-calendar:before {\n  border-width: 5px;\n  margin: 0 -5px;\n}\n.flatpickr-calendar:after {\n  border-width: 4px;\n  margin: 0 -4px;\n}\n.flatpickr-calendar.arrowTop:before,\n.flatpickr-calendar.arrowTop:after {\n  bottom: 100%;\n}\n.flatpickr-calendar.arrowTop:before {\n  border-bottom-color: #e6e6e6;\n}\n.flatpickr-calendar.arrowTop:after {\n  border-bottom-color: #fff;\n}\n.flatpickr-calendar.arrowBottom:before,\n.flatpickr-calendar.arrowBottom:after {\n  top: 100%;\n}\n.flatpickr-calendar.arrowBottom:before {\n  border-top-color: #e6e6e6;\n}\n.flatpickr-calendar.arrowBottom:after {\n  border-top-color: #fff;\n}\n.flatpickr-calendar:focus {\n  outline: 0;\n}\n.flatpickr-wrapper {\n  position: relative;\n  display: inline-block;\n}\n.flatpickr-months {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n}\n.flatpickr-months .flatpickr-month {\n  background: transparent;\n  color: rgba(0,0,0,0.9);\n  fill: rgba(0,0,0,0.9);\n  height: 34px;\n  line-height: 1;\n  text-align: center;\n  position: relative;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n  overflow: hidden;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n}\n.flatpickr-months .flatpickr-prev-month,\n.flatpickr-months .flatpickr-next-month {\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n  text-decoration: none;\n  cursor: pointer;\n  position: absolute;\n  top: 0;\n  height: 34px;\n  padding: 10px;\n  z-index: 3;\n  color: rgba(0,0,0,0.9);\n  fill: rgba(0,0,0,0.9);\n}\n.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,\n.flatpickr-months .flatpickr-next-month.flatpickr-disabled {\n  display: none;\n}\n.flatpickr-months .flatpickr-prev-month i,\n.flatpickr-months .flatpickr-next-month i {\n  position: relative;\n}\n.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,\n.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {\n/*\n      /*rtl:begin:ignore*/\n/*\n      */\n  left: 0;\n/*\n      /*rtl:end:ignore*/\n/*\n      */\n}\n/*\n      /*rtl:begin:ignore*/\n/*\n      /*rtl:end:ignore*/\n.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,\n.flatpickr-months .flatpickr-next-month.flatpickr-next-month {\n/*\n      /*rtl:begin:ignore*/\n/*\n      */\n  right: 0;\n/*\n      /*rtl:end:ignore*/\n/*\n      */\n}\n/*\n      /*rtl:begin:ignore*/\n/*\n      /*rtl:end:ignore*/\n.flatpickr-months .flatpickr-prev-month:hover,\n.flatpickr-months .flatpickr-next-month:hover {\n  color: #959ea9;\n}\n.flatpickr-months .flatpickr-prev-month:hover svg,\n.flatpickr-months .flatpickr-next-month:hover svg {\n  fill: #f64747;\n}\n.flatpickr-months .flatpickr-prev-month svg,\n.flatpickr-months .flatpickr-next-month svg {\n  width: 14px;\n  height: 14px;\n}\n.flatpickr-months .flatpickr-prev-month svg path,\n.flatpickr-months .flatpickr-next-month svg path {\n  -webkit-transition: fill 0.1s;\n  transition: fill 0.1s;\n  fill: inherit;\n}\n.numInputWrapper {\n  position: relative;\n  height: auto;\n}\n.numInputWrapper input,\n.numInputWrapper span {\n  display: inline-block;\n}\n.numInputWrapper input {\n  width: 100%;\n}\n.numInputWrapper input::-ms-clear {\n  display: none;\n}\n.numInputWrapper input::-webkit-outer-spin-button,\n.numInputWrapper input::-webkit-inner-spin-button {\n  margin: 0;\n  -webkit-appearance: none;\n}\n.numInputWrapper span {\n  position: absolute;\n  right: 0;\n  width: 14px;\n  padding: 0 4px 0 2px;\n  height: 50%;\n  line-height: 50%;\n  opacity: 0;\n  cursor: pointer;\n  border: 1px solid rgba(57,57,57,0.15);\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n}\n.numInputWrapper span:hover {\n  background: rgba(0,0,0,0.1);\n}\n.numInputWrapper span:active {\n  background: rgba(0,0,0,0.2);\n}\n.numInputWrapper span:after {\n  display: block;\n  content: \"\";\n  position: absolute;\n}\n.numInputWrapper span.arrowUp {\n  top: 0;\n  border-bottom: 0;\n}\n.numInputWrapper span.arrowUp:after {\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-bottom: 4px solid rgba(57,57,57,0.6);\n  top: 26%;\n}\n.numInputWrapper span.arrowDown {\n  top: 50%;\n}\n.numInputWrapper span.arrowDown:after {\n  border-left: 4px solid transparent;\n  border-right: 4px solid transparent;\n  border-top: 4px solid rgba(57,57,57,0.6);\n  top: 40%;\n}\n.numInputWrapper span svg {\n  width: inherit;\n  height: auto;\n}\n.numInputWrapper span svg path {\n  fill: rgba(0,0,0,0.5);\n}\n.numInputWrapper:hover {\n  background: rgba(0,0,0,0.05);\n}\n.numInputWrapper:hover span {\n  opacity: 1;\n}\n.flatpickr-current-month {\n  font-size: 135%;\n  line-height: inherit;\n  font-weight: 300;\n  color: inherit;\n  position: absolute;\n  width: 75%;\n  left: 12.5%;\n  padding: 7.48px 0 0 0;\n  line-height: 1;\n  height: 34px;\n  display: inline-block;\n  text-align: center;\n  -webkit-transform: translate3d(0px, 0px, 0px);\n          transform: translate3d(0px, 0px, 0px);\n}\n.flatpickr-current-month span.cur-month {\n  font-family: inherit;\n  font-weight: 700;\n  color: inherit;\n  display: inline-block;\n  margin-left: 0.5ch;\n  padding: 0;\n}\n.flatpickr-current-month span.cur-month:hover {\n  background: rgba(0,0,0,0.05);\n}\n.flatpickr-current-month .numInputWrapper {\n  width: 6ch;\n  width: 7ch\\0;\n  display: inline-block;\n}\n.flatpickr-current-month .numInputWrapper span.arrowUp:after {\n  border-bottom-color: rgba(0,0,0,0.9);\n}\n.flatpickr-current-month .numInputWrapper span.arrowDown:after {\n  border-top-color: rgba(0,0,0,0.9);\n}\n.flatpickr-current-month input.cur-year {\n  background: transparent;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  color: inherit;\n  cursor: text;\n  padding: 0 0 0 0.5ch;\n  margin: 0;\n  display: inline-block;\n  font-size: inherit;\n  font-family: inherit;\n  font-weight: 300;\n  line-height: inherit;\n  height: auto;\n  border: 0;\n  border-radius: 0;\n  vertical-align: initial;\n  -webkit-appearance: textfield;\n  -moz-appearance: textfield;\n  appearance: textfield;\n}\n.flatpickr-current-month input.cur-year:focus {\n  outline: 0;\n}\n.flatpickr-current-month input.cur-year[disabled],\n.flatpickr-current-month input.cur-year[disabled]:hover {\n  font-size: 100%;\n  color: rgba(0,0,0,0.5);\n  background: transparent;\n  pointer-events: none;\n}\n.flatpickr-current-month .flatpickr-monthDropdown-months {\n  appearance: menulist;\n  background: transparent;\n  border: none;\n  border-radius: 0;\n  box-sizing: border-box;\n  color: inherit;\n  cursor: pointer;\n  font-size: inherit;\n  font-family: inherit;\n  font-weight: 300;\n  height: auto;\n  line-height: inherit;\n  margin: -1px 0 0 0;\n  outline: none;\n  padding: 0 0 0 0.5ch;\n  position: relative;\n  vertical-align: initial;\n  -webkit-box-sizing: border-box;\n  -webkit-appearance: menulist;\n  -moz-appearance: menulist;\n  width: auto;\n}\n.flatpickr-current-month .flatpickr-monthDropdown-months:focus,\n.flatpickr-current-month .flatpickr-monthDropdown-months:active {\n  outline: none;\n}\n.flatpickr-current-month .flatpickr-monthDropdown-months:hover {\n  background: rgba(0,0,0,0.05);\n}\n.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {\n  background-color: transparent;\n  outline: none;\n  padding: 0;\n}\n.flatpickr-weekdays {\n  background: transparent;\n  text-align: center;\n  overflow: hidden;\n  width: 100%;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  height: 28px;\n}\n.flatpickr-weekdays .flatpickr-weekdaycontainer {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n}\nspan.flatpickr-weekday {\n  cursor: default;\n  font-size: 90%;\n  background: transparent;\n  color: rgba(0,0,0,0.54);\n  line-height: 1;\n  margin: 0;\n  text-align: center;\n  display: block;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  font-weight: bolder;\n}\n.dayContainer,\n.flatpickr-weeks {\n  padding: 1px 0 0 0;\n}\n.flatpickr-days {\n  position: relative;\n  overflow: hidden;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: start;\n  -webkit-align-items: flex-start;\n      -ms-flex-align: start;\n          align-items: flex-start;\n  width: 307.875px;\n}\n.flatpickr-days:focus {\n  outline: 0;\n}\n.dayContainer {\n  padding: 0;\n  outline: 0;\n  text-align: left;\n  width: 307.875px;\n  min-width: 307.875px;\n  max-width: 307.875px;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  display: inline-block;\n  display: -ms-flexbox;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: flex;\n  -webkit-flex-wrap: wrap;\n          flex-wrap: wrap;\n  -ms-flex-wrap: wrap;\n  -ms-flex-pack: justify;\n  -webkit-justify-content: space-around;\n          justify-content: space-around;\n  -webkit-transform: translate3d(0px, 0px, 0px);\n          transform: translate3d(0px, 0px, 0px);\n  opacity: 1;\n}\n.dayContainer + .dayContainer {\n  -webkit-box-shadow: -1px 0 0 #e6e6e6;\n          box-shadow: -1px 0 0 #e6e6e6;\n}\n.flatpickr-day {\n  background: none;\n  border: 1px solid transparent;\n  border-radius: 150px;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  color: #393939;\n  cursor: pointer;\n  font-weight: 400;\n  width: 14.2857143%;\n  -webkit-flex-basis: 14.2857143%;\n      -ms-flex-preferred-size: 14.2857143%;\n          flex-basis: 14.2857143%;\n  max-width: 39px;\n  height: 39px;\n  line-height: 39px;\n  margin: 0;\n  display: inline-block;\n  position: relative;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  text-align: center;\n}\n.flatpickr-day.inRange,\n.flatpickr-day.prevMonthDay.inRange,\n.flatpickr-day.nextMonthDay.inRange,\n.flatpickr-day.today.inRange,\n.flatpickr-day.prevMonthDay.today.inRange,\n.flatpickr-day.nextMonthDay.today.inRange,\n.flatpickr-day:hover,\n.flatpickr-day.prevMonthDay:hover,\n.flatpickr-day.nextMonthDay:hover,\n.flatpickr-day:focus,\n.flatpickr-day.prevMonthDay:focus,\n.flatpickr-day.nextMonthDay:focus {\n  cursor: pointer;\n  outline: 0;\n  background: #e6e6e6;\n  border-color: #e6e6e6;\n}\n.flatpickr-day.today {\n  border-color: #959ea9;\n}\n.flatpickr-day.today:hover,\n.flatpickr-day.today:focus {\n  border-color: #959ea9;\n  background: #959ea9;\n  color: #fff;\n}\n.flatpickr-day.selected,\n.flatpickr-day.startRange,\n.flatpickr-day.endRange,\n.flatpickr-day.selected.inRange,\n.flatpickr-day.startRange.inRange,\n.flatpickr-day.endRange.inRange,\n.flatpickr-day.selected:focus,\n.flatpickr-day.startRange:focus,\n.flatpickr-day.endRange:focus,\n.flatpickr-day.selected:hover,\n.flatpickr-day.startRange:hover,\n.flatpickr-day.endRange:hover,\n.flatpickr-day.selected.prevMonthDay,\n.flatpickr-day.startRange.prevMonthDay,\n.flatpickr-day.endRange.prevMonthDay,\n.flatpickr-day.selected.nextMonthDay,\n.flatpickr-day.startRange.nextMonthDay,\n.flatpickr-day.endRange.nextMonthDay {\n  background: #569ff7;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  color: #fff;\n  border-color: #569ff7;\n}\n.flatpickr-day.selected.startRange,\n.flatpickr-day.startRange.startRange,\n.flatpickr-day.endRange.startRange {\n  border-radius: 50px 0 0 50px;\n}\n.flatpickr-day.selected.endRange,\n.flatpickr-day.startRange.endRange,\n.flatpickr-day.endRange.endRange {\n  border-radius: 0 50px 50px 0;\n}\n.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),\n.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),\n.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {\n  -webkit-box-shadow: -10px 0 0 #569ff7;\n          box-shadow: -10px 0 0 #569ff7;\n}\n.flatpickr-day.selected.startRange.endRange,\n.flatpickr-day.startRange.startRange.endRange,\n.flatpickr-day.endRange.startRange.endRange {\n  border-radius: 50px;\n}\n.flatpickr-day.inRange {\n  border-radius: 0;\n  -webkit-box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;\n          box-shadow: -5px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;\n}\n.flatpickr-day.flatpickr-disabled,\n.flatpickr-day.flatpickr-disabled:hover,\n.flatpickr-day.prevMonthDay,\n.flatpickr-day.nextMonthDay,\n.flatpickr-day.notAllowed,\n.flatpickr-day.notAllowed.prevMonthDay,\n.flatpickr-day.notAllowed.nextMonthDay {\n  color: rgba(57,57,57,0.3);\n  background: transparent;\n  border-color: transparent;\n  cursor: default;\n}\n.flatpickr-day.flatpickr-disabled,\n.flatpickr-day.flatpickr-disabled:hover {\n  cursor: not-allowed;\n  color: rgba(57,57,57,0.1);\n}\n.flatpickr-day.week.selected {\n  border-radius: 0;\n  -webkit-box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;\n          box-shadow: -5px 0 0 #569ff7, 5px 0 0 #569ff7;\n}\n.flatpickr-day.hidden {\n  visibility: hidden;\n}\n.rangeMode .flatpickr-day {\n  margin-top: 1px;\n}\n.flatpickr-weekwrapper {\n  float: left;\n}\n.flatpickr-weekwrapper .flatpickr-weeks {\n  padding: 0 12px;\n  -webkit-box-shadow: 1px 0 0 #e6e6e6;\n          box-shadow: 1px 0 0 #e6e6e6;\n}\n.flatpickr-weekwrapper .flatpickr-weekday {\n  float: none;\n  width: 100%;\n  line-height: 28px;\n}\n.flatpickr-weekwrapper span.flatpickr-day,\n.flatpickr-weekwrapper span.flatpickr-day:hover {\n  display: block;\n  width: 100%;\n  max-width: none;\n  color: rgba(57,57,57,0.3);\n  background: transparent;\n  cursor: default;\n  border: none;\n}\n.flatpickr-innerContainer {\n  display: block;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  overflow: hidden;\n}\n.flatpickr-rContainer {\n  display: inline-block;\n  padding: 0;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n}\n.flatpickr-time {\n  text-align: center;\n  outline: 0;\n  display: block;\n  height: 0;\n  line-height: 40px;\n  max-height: 40px;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  overflow: hidden;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n}\n.flatpickr-time:after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n.flatpickr-time .numInputWrapper {\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  width: 40%;\n  height: 40px;\n  float: left;\n}\n.flatpickr-time .numInputWrapper span.arrowUp:after {\n  border-bottom-color: #393939;\n}\n.flatpickr-time .numInputWrapper span.arrowDown:after {\n  border-top-color: #393939;\n}\n.flatpickr-time.hasSeconds .numInputWrapper {\n  width: 26%;\n}\n.flatpickr-time.time24hr .numInputWrapper {\n  width: 49%;\n}\n.flatpickr-time input {\n  background: transparent;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  border: 0;\n  border-radius: 0;\n  text-align: center;\n  margin: 0;\n  padding: 0;\n  height: inherit;\n  line-height: inherit;\n  color: #393939;\n  font-size: 14px;\n  position: relative;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  -webkit-appearance: textfield;\n  -moz-appearance: textfield;\n  appearance: textfield;\n}\n.flatpickr-time input.flatpickr-hour {\n  font-weight: bold;\n}\n.flatpickr-time input.flatpickr-minute,\n.flatpickr-time input.flatpickr-second {\n  font-weight: 400;\n}\n.flatpickr-time input:focus {\n  outline: 0;\n  border: 0;\n}\n.flatpickr-time .flatpickr-time-separator,\n.flatpickr-time .flatpickr-am-pm {\n  height: inherit;\n  float: left;\n  line-height: inherit;\n  color: #393939;\n  font-weight: bold;\n  width: 2%;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n      -ms-user-select: none;\n          user-select: none;\n  -webkit-align-self: center;\n      -ms-flex-item-align: center;\n          align-self: center;\n}\n.flatpickr-time .flatpickr-am-pm {\n  outline: 0;\n  width: 18%;\n  cursor: pointer;\n  text-align: center;\n  font-weight: 400;\n}\n.flatpickr-time input:hover,\n.flatpickr-time .flatpickr-am-pm:hover,\n.flatpickr-time input:focus,\n.flatpickr-time .flatpickr-am-pm:focus {\n  background: #eee;\n}\n.flatpickr-input[readonly] {\n  cursor: pointer;\n}\n@-webkit-keyframes fpFadeInDown {\n  from {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -20px, 0);\n            transform: translate3d(0, -20px, 0);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translate3d(0, 0, 0);\n            transform: translate3d(0, 0, 0);\n  }\n}\n@keyframes fpFadeInDown {\n  from {\n    opacity: 0;\n    -webkit-transform: translate3d(0, -20px, 0);\n            transform: translate3d(0, -20px, 0);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translate3d(0, 0, 0);\n            transform: translate3d(0, 0, 0);\n  }\n}\n", "$flatpickr-tile-height: 45px;\r\n\r\n.flatpickr-calendar.inline {\r\n  background: transparent;\r\n  box-shadow: none;\r\n  width: 100%;\r\n  \r\n  .flatpickr-days {\r\n    width: 100%;\r\n  }\r\n\r\n  .dayContainer {\r\n    width: 100%;\r\n    min-width: 100%;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .flatpickr-day {\r\n    border-radius: $border-radius;\r\n    max-width: inherit;\r\n    height: $flatpickr-tile-height;\r\n    line-height: $flatpickr-tile-height;\r\n\r\n    &.today {\r\n      border: 0;\r\n\r\n      &:before {\r\n        content: \"\";\r\n        display: inline-block;\r\n        border-color: rgba(0,0,0,.2) transparent $primary;\r\n        border-style: solid;\r\n        border-width: 0 0 7px 7px;\r\n        position: absolute;\r\n        bottom: 4px;\r\n        right: 4px;\r\n      }\r\n\r\n      &.selected:before {\r\n        border-color: rgba(0,0,0,.2) transparent $white;\r\n      }\r\n\r\n      &:hover {\r\n        background: #e6e6e6;\r\n        color: $black;\r\n      }\r\n    }\r\n\r\n    &.selected {\r\n      &,\r\n      &:hover,\r\n      &:focus {\r\n        border-radius: $border-radius;\r\n        background: $primary;\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  .flatpickr-weekdays {\r\n    height: $flatpickr-tile-height;\r\n  }\r\n  .flatpickr-weekday {\r\n    height: $flatpickr-tile-height;\r\n    line-height: $flatpickr-tile-height;\r\n  }\r\n\r\n  .flatpickr-months {\r\n    .flatpickr-month {\r\n      height: $flatpickr-tile-height;\r\n    }\r\n\r\n    .flatpickr-prev-month,\r\n    .flatpickr-next-month {\r\n      height: $flatpickr-tile-height;\r\n    }\r\n  }\r\n\r\n  .flatpickr-current-month {\r\n    padding-top: 0;\r\n    line-height: $flatpickr-tile-height;\r\n    height: $flatpickr-tile-height;\r\n\r\n    .flatpickr-monthDropdown-months {\r\n      appearance: none;\r\n    }\r\n    \r\n    .flatpickr-monthDropdown-months,\r\n    input.cur-year {\r\n      font-weight: $font-weight-normal;\r\n      font-size: $h4-font-size;\r\n    }\r\n  }\r\n\r\n  .flatpickr-prev-month,\r\n  .flatpickr-next-month {\r\n    width: 45px;\r\n    border-radius: $border-radius;\r\n\r\n    &:hover {\r\n      background: #e6e6e6;\r\n      color: $black;\r\n    }\r\n  }\r\n}", ".simplebar-scrollbar:before {\r\n  background: $simplebar-scrollbar-bg;\r\n}\r\n\r\n.simplebar-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  padding-bottom: 0 !important;\r\n}\r\n\r\n[data-simplebar] {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  width: $sidebar-width;\r\n}"], "names": [], "sourceRoot": ""}